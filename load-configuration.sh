#!/bin/bash
# this script is mainly for <PERSON> to download the file
# You can checkout https://bitbucket.org/fleetshipteam/paris2-configuration for local development
COMMIT_ID=$(curl -s --user $BITBUCKET_USER_NAME:$BITBUCKET_APP_PASSWORD "https://api.bitbucket.org/2.0/repositories/fleetshipteam/paris2-configuration/commits/$CONFIG_BRANCH?limit=1" | jq -r '.values[0].hash')

echo ${COMMIT_ID}

curl -s -S --user $BITBUCKET_USER_NAME:$BITBUCKET_APP_PASSWORD -L -O "https://api.bitbucket.org/2.0/repositories/fleetshipteam/paris2-configuration/src/${COMMIT_ID}/$ENV/paris2-configuration.json"

#P2_CONFIG=$(curl -s -S --user $BITBUCKET_USER_NAME:$BITBUCKET_APP_PASSWORD -L -O "https://api.bitbucket.org/2.0/repositories/fleetshipteam/paris2-configuration/src/${COMMIT_ID}/$ENV/paris2-configuration.json")



add_env()
{
  echo $1=$(jq -r ".$2" ./paris2-configuration.json) >> paris2-configuration.env
}
echo '' > paris2-configuration.env
add_env API_RISK_ASSESSMENT_URL api.base_urls.risk_assessment
add_env API_VESSEL_BASE_URL api.base_urls.paris_api
