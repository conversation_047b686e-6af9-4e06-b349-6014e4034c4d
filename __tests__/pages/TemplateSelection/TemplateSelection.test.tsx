import React from 'react';
import {render, screen, fireEvent, waitFor} from '@testing-library/react';
import {MemoryRouter} from 'react-router-dom';
import TemplateSelection from '../../../src/pages/TemplateSelection/TemplateSelection';

const mockNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
}));

jest.mock('../../../src/pages/TemplateSelection/components/TemplateSelectionFilters', () => ({
  __esModule: true,
  default: ({filters, onFilterChange}: any) => (
    <div data-testid="template-selection-filters-mock">
      <button onClick={() => onFilterChange('search', 'test-search')}>Change Filter</button>
    </div>
  ),
}));

jest.mock('../../../src/pages/RATemplateListing/components/MostlyUsedCard', () => ({
  MostlyUsedCard: (props: any) => (
    <div data-testid="mostly-used-card" onClick={() => props.onClick(props.templateId)}>
      {props.templateName}
      {props.isSelected && <span>Selected</span>}
    </div>
  ),
  MostlyUsedCardList: (props: any) => (
    <div data-testid="mostly-used-card-list">
      <button onClick={() => props.onClick(1)}>Select Card</button>
      {props.selectedCardId && <span>Card {props.selectedCardId} selected</span>}
      {props.extraFooterOptions}
    </div>
  ),
}));

jest.mock('../../../src/components/CardGallery', () => (props: any) => (
  <div data-testid="card-gallery">
    {props.data.map((item: any, index: number) => (
      <div key={index}>{props.renderItem(item, item.id)}</div>
    ))}
    <button onClick={props.fetchNextPage}>Fetch More</button>
  </div>
));

jest.mock('../../../src/hooks', () => ({
  useInfiniteQuery: () => ({
    data: {
      data: [
        {
          id: 1,
          task_requiring_ra: 'Task 1',
          template_category: [],
          template_hazards: [],
          template_keywords: [],
          created_at: '2024-01-01',
          created_by: 2,
        },
      ],
      userDetails: [
        {userId: 2, full_name: 'John Doe'},
      ],
      pagination: {},
    },
    isFetchingNextPage: false,
    isLoading: false,
    fetchNextPage: jest.fn(),
  }),
}));

jest.mock('../../../src/components/BottomButton', () => (props: any) => (
  <div data-testid="bottom-button">
    {props.buttons.map((btn: any) => (
      <button
        key={btn.title}
        disabled={btn.disabled}
        onClick={btn.onClick}
        data-testid={btn.testID}
      >
        {btn.title}
      </button>
    ))}
  </div>
));

jest.mock('../../../src/components/icons', () => ({ExternalLinkIcon: () => <span>ExternalLinkIcon</span>}));

// Helper to mock useInfiniteQuery before importing TemplateSelection
function setupUseInfiniteQueryMock(mockImpl: any) {
  jest.resetModules();
  jest.doMock('../../../src/hooks', () => ({
    useInfiniteQuery: mockImpl,
  }));
}

describe('TemplateSelection', () => {
  beforeEach(() => {
    mockNavigate.mockClear();
  });

  afterEach(() => {
    jest.resetModules();
    jest.dontMock('../../../src/hooks');
  });
  it('renders main UI and handles template selection', () => {
    render(
      <MemoryRouter>
        <TemplateSelection />
      </MemoryRouter>
    );
    expect(screen.getByText('Creating Risk Assessment')).toBeInTheDocument();
    expect(screen.getByText('Select a Template')).toBeInTheDocument();
    expect(screen.getByTestId('template-selection-filters-mock')).toBeInTheDocument();
    expect(screen.getByTestId('mostly-used-card-list')).toBeInTheDocument();
    expect(screen.getByTestId('card-gallery')).toBeInTheDocument();
    expect(screen.getByTestId('bottom-button')).toBeInTheDocument();
  });

  it('selects a template from MostlyUsedCardList and enables Use Template button', () => {
    render(
      <MemoryRouter>
        <TemplateSelection />
      </MemoryRouter>
    );
    const selectBtn = screen.getByText('Select Card');
    fireEvent.click(selectBtn);
    expect(screen.getByText('Card 1 selected')).toBeInTheDocument();
    const useTemplateBtn = screen.getByTestId('form-prj-save-btn');
    expect(useTemplateBtn).not.toBeDisabled();
  });

  it('disables Use Template button if no template is selected', () => {
    render(
      <MemoryRouter>
        <TemplateSelection />
      </MemoryRouter>
    );
    const useTemplateBtn = screen.getByTestId('form-prj-save-btn');
    expect(useTemplateBtn).toBeDisabled();
  });

  it('calls navigation on Cancel button click', () => {
    render(
      <MemoryRouter>
        <TemplateSelection />
      </MemoryRouter>
    );
    fireEvent.click(screen.getByTestId('form-prj-cancel-btn'));
    expect(mockNavigate).toHaveBeenCalledWith('/risk-assessment');
  });

  it('calls navigation on ExternalLinkIcon button click', () => {
    render(
      <MemoryRouter>
        <TemplateSelection />
      </MemoryRouter>
    );
    fireEvent.click(screen.getByTestId('open-templates-btn'));
  });

  it('calls navigation on Use Template click', () => {
    render(
      <MemoryRouter>
        <TemplateSelection />
      </MemoryRouter>
    );
    fireEvent.click(screen.getByText('Select Card'));
    fireEvent.click(screen.getByTestId('form-prj-save-btn'));
    expect(mockNavigate).toHaveBeenCalledWith('/risk-assessment/templates/1/risks/create');
  });

  it('fetches more templates when Fetch More is clicked', () => {
    render(
      <MemoryRouter>
        <TemplateSelection />
      </MemoryRouter>
    );
    fireEvent.click(screen.getByText('Fetch More'));
    // No error means fetchNextPage was called (mocked)
  });

  it('resets selectedTemplate when handleFilterChange is called', () => {
    render(
      <MemoryRouter>
        <TemplateSelection />
      </MemoryRouter>
    );
    // Select a card first
    fireEvent.click(screen.getByText('Select Card'));
    expect(screen.getByText('Card 1 selected')).toBeInTheDocument();
    // Now change filter
    fireEvent.click(screen.getByText('Change Filter'));
    // Card selection should be reset
    expect(screen.queryByText('Card 1 selected')).not.toBeInTheDocument();
  });

  it('displays correct button text', () => {
    render(
      <MemoryRouter>
        <TemplateSelection />
      </MemoryRouter>
    );
    expect(screen.getByText('Use Template')).toBeInTheDocument();
    expect(screen.getByText('Cancel')).toBeInTheDocument();
  });

  it('renders template cards from CardGallery', () => {
    render(
      <MemoryRouter>
        <TemplateSelection />
      </MemoryRouter>
    );
    expect(screen.getByTestId('card-gallery')).toBeInTheDocument();
    expect(screen.getByText('Fetch More')).toBeInTheDocument();
  });

  it('renders All Templates section', () => {
    render(
      <MemoryRouter>
        <TemplateSelection />
      </MemoryRouter>
    );
    expect(screen.getByText('All Templates')).toBeInTheDocument();
  });

  it('renders external link icon in template preview button', () => {
    render(
      <MemoryRouter>
        <TemplateSelection />
      </MemoryRouter>
    );
    expect(screen.getByText('ExternalLinkIcon')).toBeInTheDocument();
  });
});
