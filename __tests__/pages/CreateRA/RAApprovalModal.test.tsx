import React from 'react';
import { render, fireEvent, screen, waitFor } from '@testing-library/react';
import RAApprovalModal, { ApprovalOperationType } from '../../../src/pages/CreateRA/RAApprovalModal';
import { toast } from 'react-toastify';

// Mock react-toastify to avoid actual toasts
jest.mock('react-toastify');

// Mock CustomDatePicker to call onChange with a real Date object
jest.mock('../../../src/components/CustomDatePicker', () => ({
  __esModule: true,
  default: ({ onChange, value, ...props }: any) => (
    <input
      type="text"
      placeholder={props.placeholder}
      value={value ? value.toISOString().slice(0, 10) : ''}
      onChange={e => onChange(new Date(e.target.value))}
      data-testid="mock-datepicker"
    />
  ),
}));

// Mock axios to provide AxiosError for instanceof checks
jest.mock('axios', () => {
  class AxiosError extends Error {
    constructor(message: string, response?: any) {
      super(message);
      this.response = response;
    }
    response?: any;
  }
  return { AxiosError };
});

const onConfirm = jest.fn(async () => ({ message: 'Success!' }));
const triggerText = 'Open Modal';
const getTrigger = () => <button>{triggerText}</button>;

describe('RAApprovalModal', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('opens modal on trigger click and closes on cancel', async () => {
    render(
      <RAApprovalModal onConfirm={onConfirm} trigger={getTrigger()} />
    );
    fireEvent.click(screen.getByText(triggerText));
    expect(screen.getByText('Approving Risk Assessment')).toBeInTheDocument();
    fireEvent.click(screen.getByText('Cancel'));
    await waitFor(() => {
      expect(screen.queryByText('Approving Risk Assessment')).not.toBeInTheDocument();
    });
  });

  it('shows approval UI and disables Approve button until date is picked', () => {
    render(
      <RAApprovalModal onConfirm={onConfirm} trigger={getTrigger()} operationType="approve" />
    );
    fireEvent.click(screen.getByText(triggerText));
    expect(screen.getByText('Do you really want to Approve the RA? This action is not revertible.')).toBeInTheDocument();
    const approveBtn = screen.getByText('Approve');
    expect(approveBtn).toBeDisabled();
  });

  it('shows rejection UI and disables Reject button until date and comment are filled', () => {
    render(
      <RAApprovalModal onConfirm={onConfirm} trigger={getTrigger()} operationType="reject" />
    );
    fireEvent.click(screen.getByText(triggerText));
    expect(screen.getByText('Do you want to Reject the Risk Assessment?')).toBeInTheDocument();
    const rejectBtn = screen.getByText('Reject');
    expect(rejectBtn).toBeDisabled();
  });

  it('shows approveWithComment UI and disables Approve until date and comment are filled', () => {
    render(
      <RAApprovalModal onConfirm={onConfirm} trigger={getTrigger()} operationType="approveWithComment" />
    );
    fireEvent.click(screen.getByText(triggerText));
    expect(screen.getByText('Condition for Approval')).toBeInTheDocument();
    const approveBtn = screen.getByText('Approve');
    expect(approveBtn).toBeDisabled();
  });

  it('calls onConfirm and shows toast on approve', async () => {
    render(
      <RAApprovalModal onConfirm={onConfirm} trigger={getTrigger()} operationType="approve" />
    );
    fireEvent.click(screen.getByText(triggerText));
    fireEvent.change(screen.getByTestId('mock-datepicker'), { target: { value: '2025-07-07' } });
    fireEvent.click(screen.getByText('Approve'));
    await waitFor(() => expect(onConfirm).toHaveBeenCalled());
    expect(require('react-toastify').toast.success).toHaveBeenCalledWith('Success!');
  });

  it('calls onConfirm and shows toast on reject', async () => {
    render(
      <RAApprovalModal onConfirm={onConfirm} trigger={getTrigger()} operationType="reject" />
    );
    fireEvent.click(screen.getByText(triggerText));
    fireEvent.change(screen.getByTestId('mock-datepicker'), { target: { value: '2025-07-07' } });
    fireEvent.change(screen.getByPlaceholderText('Type the Reason for Rejection'), { target: { value: 'Not safe' } });
    fireEvent.click(screen.getByText('Reject'));
    await waitFor(() => expect(onConfirm).toHaveBeenCalled());
    expect(require('react-toastify').toast.success).toHaveBeenCalledWith('Success!');
  });

  it('shows error toast if onConfirm throws', async () => {
    const errorOnConfirm = jest.fn().mockRejectedValue(new Error('fail'));
    render(
      <RAApprovalModal onConfirm={errorOnConfirm} trigger={getTrigger()} operationType="approve" />
    );
    fireEvent.click(screen.getByText(triggerText));
    fireEvent.change(screen.getByTestId('mock-datepicker'), { target: { value: '2025-07-07' } });
    fireEvent.click(screen.getByText('Approve'));
    await waitFor(() => expect(require('react-toastify').toast.error).toHaveBeenCalled());
  });

  it('approveWithComment: enables Approve when date and comment are filled, calls onConfirm with correct params', async () => {
    render(
      <RAApprovalModal onConfirm={onConfirm} trigger={getTrigger()} operationType="approveWithComment" />
    );
    fireEvent.click(screen.getByText(triggerText));
    const approveBtn = screen.getByText('Approve');
    fireEvent.change(screen.getByTestId('mock-datepicker'), { target: { value: '2025-07-07' } });
    expect(approveBtn).toBeDisabled();
    fireEvent.change(screen.getByPlaceholderText('Type the Condition for Approval'), { target: { value: 'Condition' } });
    expect(approveBtn).not.toBeDisabled();
    fireEvent.click(approveBtn);
    await waitFor(() => expect(onConfirm).toHaveBeenCalledWith(expect.objectContaining({
      operationType: 'approveWithComment',
      comment: 'Condition',
    })));
  });

  it('reject: enables Reject when date and comment are filled, calls onConfirm with correct params', async () => {
    render(
      <RAApprovalModal onConfirm={onConfirm} trigger={getTrigger()} operationType="reject" />
    );
    fireEvent.click(screen.getByText(triggerText));
    const rejectBtn = screen.getByText('Reject');
    fireEvent.change(screen.getByTestId('mock-datepicker'), { target: { value: '2025-07-07' } });
    expect(rejectBtn).toBeDisabled();
    fireEvent.change(screen.getByPlaceholderText('Type the Reason for Rejection'), { target: { value: 'Not safe' } });
    expect(rejectBtn).not.toBeDisabled();
    fireEvent.click(rejectBtn);
    await waitFor(() => expect(onConfirm).toHaveBeenCalledWith(expect.objectContaining({
      operationType: 'reject',
      comment: 'Not safe',
    })));
  });

  it('approve: enables Approve when date is filled, calls onConfirm with correct params', async () => {
    render(
      <RAApprovalModal onConfirm={onConfirm} trigger={getTrigger()} operationType="approve" />
    );
    fireEvent.click(screen.getByText(triggerText));
    const approveBtn = screen.getByText('Approve');
    fireEvent.change(screen.getByTestId('mock-datepicker'), { target: { value: '2025-07-07' } });
    expect(approveBtn).not.toBeDisabled();
    fireEvent.click(approveBtn);
    await waitFor(() => expect(onConfirm).toHaveBeenCalledWith(expect.objectContaining({
      operationType: 'approve',
      actionDate: expect.anything(),
    })));
  });

  it('resets state on close', async () => {
    render(
      <RAApprovalModal onConfirm={onConfirm} trigger={getTrigger()} operationType="approveWithComment" />
    );
    fireEvent.click(screen.getByText(triggerText));
    fireEvent.change(screen.getByTestId('mock-datepicker'), { target: { value: '2025-07-07' } });
    fireEvent.change(screen.getByPlaceholderText('Type the Condition for Approval'), { target: { value: 'Condition' } });
    fireEvent.click(screen.getByText('Cancel'));
    await waitFor(() => expect(screen.queryByText('Approving Risk Assessment')).not.toBeInTheDocument());
    fireEvent.click(screen.getByText(triggerText));
    expect(screen.getByTestId('mock-datepicker')).toHaveValue('');
    expect(screen.getByPlaceholderText('Type the Condition for Approval')).toHaveValue('');
  });

  it('clones trigger and preserves other props', () => {
    const customTrigger = <button data-testid="custom-trigger" aria-label="modal-trigger">Open Modal</button>;
    render(
      <RAApprovalModal onConfirm={onConfirm} trigger={customTrigger} operationType="approve" />
    );
    fireEvent.click(screen.getByTestId('custom-trigger'));
    expect(screen.getByText('Approving Risk Assessment')).toBeInTheDocument();
  });

  it('does not break if trigger is null', () => {
    render(
      <RAApprovalModal onConfirm={onConfirm} trigger={null as any} operationType="approve" />
    );
    // Should not throw or render modal
    expect(screen.queryByText('Approving Risk Assessment')).not.toBeInTheDocument();
  });
});
