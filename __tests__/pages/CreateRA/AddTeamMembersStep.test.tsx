import React from 'react';
import {render, screen, fireEvent, waitFor} from '@testing-library/react';
import '@testing-library/jest-dom';
import {AddTeamMembersStep} from '../../../src/pages/CreateRA/AddTeamMembersStep';
import {RiskForm} from '../../../src/types/risk';
import {getCrewList} from '../../../src/services/services';

// Mock dependencies
jest.mock('../../../src/services/services', () => ({
  getCrewList: jest.fn(),
}));

jest.mock('../../../src/components/SearchCrewMember', () => {
  return function MockSearchCrewMember({
    value,
    options,
    placeholder,
    onChange,
  }: any) {
    return (
      <div data-testid="search-crew-member">
        <input
          data-testid="search-input"
          placeholder={placeholder}
          onChange={(e) => {
            // Simulate selecting the first option when typing
            if (e.target.value && options.length > 0) {
              onChange([options[0].id]);
            }
          }}
        />
        <div data-testid="options-count">{options.length}</div>
      </div>
    );
  };
});

jest.mock('../../../src/components/UsernameProfile', () => {
  return {
    UsernameProfile: function MockUsernameProfile({username, subText}: any) {
      return (
        <div data-testid="username-profile">
          <span data-testid="username">{username}</span>
          <span data-testid="subtext">{subText}</span>
        </div>
      );
    },
  };
});

jest.mock('../../../src/utils/svgIcons', () => ({
  CrewIcon: () => <div data-testid="crew-icon">CrewIcon</div>,
  CrossIcon: () => <div data-testid="cross-icon">CrossIcon</div>,
}));

// Mock date-fns
jest.mock('date-fns', () => ({
  format: jest.fn((date, formatStr) => '15 Jan 2024'),
}));

describe('AddTeamMembersStep Component', () => {
  const mockSetForm = jest.fn();
  const mockOnValidate = jest.fn();
  const mockGetCrewList = getCrewList as jest.MockedFunction<typeof getCrewList>;

  const mockCrewList = [
    {
      seafarer_id: 1,
      seafarer_person_id: 101,
      seafarer_hkid: 12345,
      seafarer_rank_id: 1,
      seafarer_name: 'John Doe',
      seafarer_rank: 'Captain',
      seafarer_rank_sort_order: 1,
    },
    {
      seafarer_id: 2,
      seafarer_person_id: 102,
      seafarer_hkid: 67890,
      seafarer_rank_id: 2,
      seafarer_name: 'Jane Smith',
      seafarer_rank: 'Chief Engineer',
      seafarer_rank_sort_order: 2,
    },
  ];

  const defaultForm: RiskForm = {
    id: 1,
    template_id: 1,
    task_requiring_ra: 'Test Task',
    assessor: 1,
    vessel_ownership_id: 1,
    vessel_id: 123,
    date_risk_assessment: '2024-01-15',
    task_duration: '5',
    task_alternative_consideration: 'Alternative',
    task_rejection_reason: 'Reason',
    worst_case_scenario: 'Scenario',
    recovery_measures: 'Measures',
    status: 'draft',
    approval_required: [],
    risk_team_member: [],
    risk_category: {
      is_other: false,
      category_id: [],
      value: '',
    },
    risk_hazard: {
      is_other: false,
      hazard_id: [],
      value: '',
    },
    parameters: [],
    risk_job: [],
    risk_task_reliability_assessment: [],
    created_at: '2024-01-15T00:00:00Z',
    updated_at: '2024-01-15T00:00:00Z',
  };

  const formWithTeamMembers: RiskForm = {
    ...defaultForm,
    risk_team_member: [mockCrewList[0]],
  };

  beforeEach(() => {
    jest.clearAllMocks();
    mockGetCrewList.mockResolvedValue(mockCrewList);
  });

  const renderAddTeamMembersStep = (
    form = defaultForm,
    setForm = mockSetForm,
    onValidate = mockOnValidate,
  ) => {
    const ref = React.createRef<any>();
    render(
      <AddTeamMembersStep
        ref={ref}
        form={form}
        setForm={setForm}
        onValidate={onValidate}
      />
    );
    return {ref};
  };

  describe('Component Rendering', () => {
    it('renders the component with all required elements', () => {
      renderAddTeamMembersStep();

      expect(screen.getByText('Test Task')).toBeInTheDocument();
      expect(screen.getByText('Date of Risk Assessment: 15 Jan 2024')).toBeInTheDocument();
      expect(screen.getByText('Add Team Members')).toBeInTheDocument();
      expect(screen.getByTestId('search-crew-member')).toBeInTheDocument();
    });

    it('renders empty state when no team members are added', () => {
      renderAddTeamMembersStep();

      expect(screen.getByTestId('crew-icon')).toBeInTheDocument();
      expect(screen.getByText(/Search and Add the Team Members/)).toBeInTheDocument();
    });

    it('renders team members when they exist', () => {
      renderAddTeamMembersStep(formWithTeamMembers);

      expect(screen.getByTestId('username-profile')).toBeInTheDocument();
      expect(screen.getByTestId('username')).toHaveTextContent('John Doe');
      expect(screen.getByTestId('subtext')).toHaveTextContent('Captain • HK ID: 12345');
    });

    it('applies correct CSS classes based on team member count', () => {
      const {rerender} = render(
        <AddTeamMembersStep
          ref={React.createRef()}
          form={defaultForm}
          setForm={mockSetForm}
          onValidate={mockOnValidate}
        />
      );

      // Check empty state classes
      const cardBody = document.querySelector('.card-body');
      expect(cardBody).toHaveClass('d-flex justify-content-center align-items-center');

      rerender(
        <AddTeamMembersStep
          ref={React.createRef()}
          form={formWithTeamMembers}
          setForm={mockSetForm}
          onValidate={mockOnValidate}
        />
      );

      // Component should render without errors and with different classes
      expect(screen.getByTestId('username-profile')).toBeInTheDocument();
      const cardBodyWithMembers = document.querySelector('.card-body');
      expect(cardBodyWithMembers).toHaveClass('p-3');
    });
  });

  describe('Crew List Fetching', () => {
    it('fetches crew list on component mount', async () => {
      renderAddTeamMembersStep();

      await waitFor(() => {
        expect(mockGetCrewList).toHaveBeenCalledWith(123);
      });
    });

    it('fetches crew list when vessel_id changes', async () => {
      const {rerender} = render(
        <AddTeamMembersStep
          ref={React.createRef()}
          form={defaultForm}
          setForm={mockSetForm}
          onValidate={mockOnValidate}
        />
      );

      await waitFor(() => {
        expect(mockGetCrewList).toHaveBeenCalledWith(123);
      });

      const updatedForm = {...defaultForm, vessel_id: 456};
      rerender(
        <AddTeamMembersStep
          ref={React.createRef()}
          form={updatedForm}
          setForm={mockSetForm}
          onValidate={mockOnValidate}
        />
      );

      await waitFor(() => {
        expect(mockGetCrewList).toHaveBeenCalledWith(456);
      });
    });

    it('handles crew list fetch error gracefully', async () => {
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();
      mockGetCrewList.mockRejectedValue(new Error('Fetch failed'));

      renderAddTeamMembersStep();

      await waitFor(() => {
        expect(consoleSpy).toHaveBeenCalledWith('Error fetching options:', expect.any(Error));
      });

      consoleSpy.mockRestore();
    });

    it('does not fetch crew list when vessel_id is not present', async () => {
      const formWithoutVessel = {...defaultForm, vessel_id: undefined};
      renderAddTeamMembersStep(formWithoutVessel);

      await waitFor(() => {
        expect(mockGetCrewList).not.toHaveBeenCalled();
      });
    });

    it('does not fetch crew list when vessel_id is falsy', async () => {
      const formWithNullVessel = {...defaultForm, vessel_id: 0};
      renderAddTeamMembersStep(formWithNullVessel);

      await waitFor(() => {
        expect(mockGetCrewList).not.toHaveBeenCalled();
      });
    });
  });

  describe('Team Member Addition', () => {
    it('adds a team member when selected from search', async () => {
      renderAddTeamMembersStep();

      await waitFor(() => {
        expect(screen.getByTestId('options-count')).toHaveTextContent('2');
      });

      const searchInput = screen.getByTestId('search-input');
      fireEvent.change(searchInput, {target: {value: 'John'}});

      await waitFor(() => {
        expect(mockSetForm).toHaveBeenCalledWith(expect.any(Function));
      });

      // Test the callback function
      const setFormCallback = mockSetForm.mock.calls[0][0];
      const result = setFormCallback(defaultForm);

      expect(result.risk_team_member).toHaveLength(1);
      expect(result.risk_team_member[0]).toEqual(mockCrewList[0]);
    });

    it('does not add duplicate team members', async () => {
      renderAddTeamMembersStep(formWithTeamMembers);

      await waitFor(() => {
        expect(screen.getByTestId('options-count')).toHaveTextContent('2');
      });

      const searchInput = screen.getByTestId('search-input');
      fireEvent.change(searchInput, {target: {value: 'John'}});

      // Should not call setForm since member already exists
      expect(mockSetForm).not.toHaveBeenCalled();
    });

    it('handles empty selection gracefully', async () => {
      renderAddTeamMembersStep();

      await waitFor(() => {
        expect(screen.getByTestId('options-count')).toHaveTextContent('2');
      });

      const searchInput = screen.getByTestId('search-input');
      fireEvent.change(searchInput, {target: {value: ''}});

      expect(mockSetForm).not.toHaveBeenCalled();
    });

    it('handles selection when crew list is empty', async () => {
      // Set empty crew list to test edge case
      mockGetCrewList.mockResolvedValue([]);

      renderAddTeamMembersStep();

      await waitFor(() => {
        expect(mockGetCrewList).toHaveBeenCalled();
        expect(screen.getByTestId('options-count')).toHaveTextContent('0');
      });

      const searchInput = screen.getByTestId('search-input');
      fireEvent.change(searchInput, {target: {value: 'any'}});

      // Should not call setForm since there are no crew members
      expect(mockSetForm).not.toHaveBeenCalled();
    });

  });

  describe('Team Member Removal', () => {
    it('removes a team member when cross button is clicked', () => {
      renderAddTeamMembersStep(formWithTeamMembers);

      const removeButton = screen.getByTestId('cross-icon').closest('button');
      expect(removeButton).toBeInTheDocument();

      fireEvent.click(removeButton!);

      expect(mockSetForm).toHaveBeenCalledWith(expect.any(Function));

      // Test the callback function
      const setFormCallback = mockSetForm.mock.calls[0][0];
      const result = setFormCallback(formWithTeamMembers);

      expect(result.risk_team_member).toHaveLength(0);
    });

    it('handles removal when risk_team_member is undefined', () => {
      const formWithUndefinedMembers = {
        ...defaultForm,
        risk_team_member: undefined as any,
      };

      renderAddTeamMembersStep(formWithUndefinedMembers);

      // Should not crash when trying to remove from undefined array
      expect(screen.getByTestId('crew-icon')).toBeInTheDocument();
    });

    it('removes correct team member by ID', () => {
      const formWithMultipleMembers = {
        ...defaultForm,
        risk_team_member: mockCrewList,
      };

      renderAddTeamMembersStep(formWithMultipleMembers);

      const removeButtons = screen.getAllByTestId('cross-icon');
      expect(removeButtons).toHaveLength(2);

      // Click the first remove button (should remove John Doe with ID 1)
      fireEvent.click(removeButtons[0].closest('button')!);

      expect(mockSetForm).toHaveBeenCalledWith(expect.any(Function));

      const setFormCallback = mockSetForm.mock.calls[0][0];
      const result = setFormCallback(formWithMultipleMembers);

      expect(result.risk_team_member).toHaveLength(1);
      expect(result.risk_team_member[0].seafarer_id).toBe(2); // Jane Smith should remain
    });
  });

  describe('Validation', () => {
    it('validates as invalid when no team members are present', () => {
      const {ref} = renderAddTeamMembersStep();

      const isValid = ref.current?.validate();

      expect(isValid).toBe(false);
      expect(mockOnValidate).toHaveBeenCalledWith(false);
    });

    it('validates as valid when team members are present', () => {
      const {ref} = renderAddTeamMembersStep(formWithTeamMembers);

      const isValid = ref.current?.validate();

      expect(isValid).toBe(true);
      expect(mockOnValidate).toHaveBeenCalledWith(true);
    });

    it('calls validation when form changes', async () => {
      const {rerender} = render(
        <AddTeamMembersStep
          ref={React.createRef()}
          form={defaultForm}
          setForm={mockSetForm}
          onValidate={mockOnValidate}
        />
      );

      // Initial validation call
      await waitFor(() => {
        expect(mockOnValidate).toHaveBeenCalledWith(false);
      });

      mockOnValidate.mockClear();

      // Update form with team members
      rerender(
        <AddTeamMembersStep
          ref={React.createRef()}
          form={formWithTeamMembers}
          setForm={mockSetForm}
          onValidate={mockOnValidate}
        />
      );

      await waitFor(() => {
        expect(mockOnValidate).toHaveBeenCalledWith(true);
      });
    });

    it('does not validate when risk_team_member is not in form', () => {
      const formWithoutRiskTeamMember = {
        ...defaultForm,
      };
      delete (formWithoutRiskTeamMember as any).risk_team_member;

      renderAddTeamMembersStep(formWithoutRiskTeamMember);

      // Should not call onValidate during useEffect
      expect(mockOnValidate).not.toHaveBeenCalled();
    });
  });

  describe('Task Header', () => {
    it('displays task name correctly', () => {
      renderAddTeamMembersStep();

      expect(screen.getByText('Test Task')).toBeInTheDocument();
    });

    it('displays date when date_risk_assessment is present', () => {
      renderAddTeamMembersStep();

      expect(screen.getByText('Date of Risk Assessment: 15 Jan 2024')).toBeInTheDocument();
    });

    it('does not display date when date_risk_assessment is not present', () => {
      const formWithoutDate = {
        ...defaultForm,
      };
      delete (formWithoutDate as any).date_risk_assessment;

      renderAddTeamMembersStep(formWithoutDate);

      expect(screen.queryByText(/Date of Risk Assessment/)).not.toBeInTheDocument();
    });

    it('handles empty task name', () => {
      const formWithEmptyTask = {
        ...defaultForm,
        task_requiring_ra: '',
      };

      renderAddTeamMembersStep(formWithEmptyTask);

      // Should render empty string without crashing
      expect(screen.getByText('Add Team Members')).toBeInTheDocument();
    });
  });

  describe('Search Crew Member Integration', () => {
    it('passes correct options to SearchCrewMember', async () => {
      renderAddTeamMembersStep();

      await waitFor(() => {
        expect(screen.getByTestId('options-count')).toHaveTextContent('2');
      });

      const searchComponent = screen.getByTestId('search-crew-member');
      expect(searchComponent).toBeInTheDocument();

      const searchInput = screen.getByTestId('search-input');
      expect(searchInput).toHaveAttribute('placeholder', 'Search Name, Rank or Email ID');
    });

    it('handles empty crew list', async () => {
      mockGetCrewList.mockResolvedValue([]);

      renderAddTeamMembersStep();

      await waitFor(() => {
        expect(screen.getByTestId('options-count')).toHaveTextContent('0');
      });
    });

    it('formats crew options correctly for SearchCrewMember', async () => {
      renderAddTeamMembersStep();

      await waitFor(() => {
        expect(screen.getByTestId('options-count')).toHaveTextContent('2');
      });

      // The options should be formatted with correct structure
      // This is tested indirectly through the mock component
      expect(screen.getByTestId('search-crew-member')).toBeInTheDocument();
    });
  });

  describe('Helper Components', () => {
    describe('TeamMemberCard', () => {
      it('renders team member information correctly', () => {
        renderAddTeamMembersStep(formWithTeamMembers);

        expect(screen.getByTestId('username')).toHaveTextContent('John Doe');
        expect(screen.getByTestId('subtext')).toHaveTextContent('Captain • HK ID: 12345');
        expect(screen.getByTestId('cross-icon')).toBeInTheDocument();
      });
    });

    describe('TeamMemberList', () => {
      it('renders multiple team members', () => {
        const formWithMultipleMembers = {
          ...defaultForm,
          risk_team_member: mockCrewList,
        };

        renderAddTeamMembersStep(formWithMultipleMembers);

        const usernames = screen.getAllByTestId('username');
        expect(usernames).toHaveLength(2);
        expect(usernames[0]).toHaveTextContent('John Doe');
        expect(usernames[1]).toHaveTextContent('Jane Smith');
      });
    });

    describe('EmptyTeamMemberState', () => {
      it('renders empty state with correct message', () => {
        renderAddTeamMembersStep();

        expect(screen.getByTestId('crew-icon')).toBeInTheDocument();
        expect(screen.getByText(/Search and Add the Team Members/)).toBeInTheDocument();
        expect(screen.getByText(/involved in preparing the Risk Assessment/)).toBeInTheDocument();
      });
    });
  });

  describe('Edge Cases and Error Handling', () => {
    it('handles form without vessel_id property', async () => {
      const formWithoutVesselId = {
        ...defaultForm,
      };
      delete (formWithoutVesselId as any).vessel_id;

      renderAddTeamMembersStep(formWithoutVesselId);

      // Should not call getCrewList
      expect(mockGetCrewList).not.toHaveBeenCalled();
    });

    it('handles adding team member when form does not have risk_team_member property', async () => {
      const formWithoutRiskTeamMember = {
        ...defaultForm,
      };
      delete (formWithoutRiskTeamMember as any).risk_team_member;

      renderAddTeamMembersStep(formWithoutRiskTeamMember);

      await waitFor(() => {
        expect(screen.getByTestId('options-count')).toHaveTextContent('2');
      });

      const searchInput = screen.getByTestId('search-input');
      fireEvent.change(searchInput, {target: {value: 'John'}});

      // Should not call setForm since risk_team_member is not in form
      expect(mockSetForm).not.toHaveBeenCalled();
    });

    it('handles null or undefined team members array gracefully', () => {
      const formWithNullMembers = {
        ...defaultForm,
        risk_team_member: null as any,
      };

      renderAddTeamMembersStep(formWithNullMembers);

      // Should render empty state without crashing
      expect(screen.getByTestId('crew-icon')).toBeInTheDocument();
    });

    it('handles removal with null team members array', () => {
      const formWithNullMembers = {
        ...defaultForm,
        risk_team_member: null as any,
      };

      renderAddTeamMembersStep(formWithNullMembers);

      // Should not crash when trying to remove from null array
      expect(screen.getByTestId('crew-icon')).toBeInTheDocument();
    });
  });

  describe('Component Lifecycle', () => {
    it('exposes validate method through ref', () => {
      const {ref} = renderAddTeamMembersStep();

      expect(ref.current).toHaveProperty('validate');
      expect(typeof ref.current?.validate).toBe('function');
    });

    it('calls validation on form changes via useEffect', async () => {
      const {rerender} = render(
        <AddTeamMembersStep
          ref={React.createRef()}
          form={defaultForm}
          setForm={mockSetForm}
          onValidate={mockOnValidate}
        />
      );

      // Clear initial calls
      mockOnValidate.mockClear();

      // Change form
      const updatedForm = {
        ...defaultForm,
        task_requiring_ra: 'Updated Task',
      };

      rerender(
        <AddTeamMembersStep
          ref={React.createRef()}
          form={updatedForm}
          setForm={mockSetForm}
          onValidate={mockOnValidate}
        />
      );

      await waitFor(() => {
        expect(mockOnValidate).toHaveBeenCalledWith(false);
      });
    });

    it('handles component unmounting gracefully', () => {
      const {unmount} = render(
        <AddTeamMembersStep
          ref={React.createRef()}
          form={defaultForm}
          setForm={mockSetForm}
          onValidate={mockOnValidate}
        />
      );

      expect(() => {
        unmount();
      }).not.toThrow();
    });
  });
});
