import React from 'react';
import {render, screen, fireEvent, waitFor} from '@testing-library/react';
import '@testing-library/jest-dom';
import {AddJobModal} from '../../../src/pages/CreateRA/AddJobModal';
import {TemplateForm, TemplateFormJob} from '../../../src/types/template';
import {RiskForm, RiskFormJob} from '../../../src/types/risk';
import {TemplateFormStatus} from '../../../src/enums';

// Mock dependencies
jest.mock('../../../src/context', () => ({
  useDataStoreContext: jest.fn(),
}));

jest.mock('../../../src/pages/CreateRA/AddJobsStep', () => {
  const mockReact = require('react');
  return {
    AddJobsStep: mockReact.forwardRef(({form, setForm, onValidate, isEdit, jobIndex, type}: any, ref: any) => {
      mockReact.useImperativeHandle(ref, () => ({
        validate: jest.fn(() => true),
      }));

      const handleFieldChange = (field: string, value: string) => {
        if (type === 'risk') {
          setForm((prev: any) => ({
            ...prev,
            risk_job: [{
              ...prev.risk_job[0],
              [field]: value,
            }],
          }));
        } else {
          setForm((prev: any) => ({
            ...prev,
            template_job: [{
              ...prev.template_job[0],
              [field]: value,
            }],
          }));
        }
        onValidate?.(true);
      };

      const job = type === 'risk'
        ? form?.risk_job?.[0]
        : form?.template_job?.[0];

      return mockReact.createElement('div', { 'data-testid': 'add-jobs-step' },
        mockReact.createElement('div', { 'data-testid': `job-index-${jobIndex}` }, `Job Index: ${jobIndex}`),
        mockReact.createElement('div', { 'data-testid': `is-edit-${isEdit}` }, `Is Edit: ${isEdit.toString()}`),
        mockReact.createElement('div', { 'data-testid': `type-${type}` }, `Type: ${type}`),
        mockReact.createElement('input', {
          'data-testid': 'job-step-input',
          value: job?.job_step || '',
          onChange: (e: any) => handleFieldChange('job_step', e.target.value),
          placeholder: 'Job Step'
        }),
        mockReact.createElement('input', {
          'data-testid': 'job-hazard-input',
          value: job?.job_hazard || '',
          onChange: (e: any) => handleFieldChange('job_hazard', e.target.value),
          placeholder: 'Job Hazard'
        })
      );
    }),
  };
});

jest.mock('uuid', () => ({
  v4: jest.fn(() => 'mock-uuid-1234'),
}));

jest.mock('lodash', () => ({
  cloneDeep: jest.fn((obj) => {
    if (obj === undefined || obj === null) return obj;
    try {
      return JSON.parse(JSON.stringify(obj));
    } catch {
      return obj;
    }
  }),
}));

describe('AddJobModal', () => {
  const mockOnClose = jest.fn();
  const mockSetForm = jest.fn();

  const mockTemplateForm: TemplateForm = {
    task_requiring_ra: 'Test Task',
    task_duration: '2 hours',
    task_alternative_consideration: 'Test consideration',
    task_rejection_reason: '',
    worst_case_scenario: 'Test scenario',
    recovery_measures: 'Test measures',
    status: TemplateFormStatus.DRAFT,
    template_category: { category_id: [1] },
    template_hazard: { is_other: false, value: '', hazard_id: [1] },
    parameters: [],
    template_job: [],
    template_task_reliability_assessment: [],
    template_keyword: [],
  };

  const mockRiskForm: RiskForm = {
    template_id: 1,
    task_requiring_ra: 'Test Risk Task',
    assessor: 1,
    vessel_ownership_id: 1,
    date_risk_assessment: '2024-01-01',
    task_duration: '3 hours',
    task_alternative_consideration: 'Risk consideration',
    task_rejection_reason: '',
    worst_case_scenario: 'Risk scenario',
    recovery_measures: 'Risk measures',
    status: 'draft',
    approval_required: [1],
    risk_team_member: [],
    risk_category: { is_other: false, category_id: [1], value: '' },
    risk_hazard: { is_other: false, hazard_id: [1], value: '' },
    parameters: [],
    risk_job: [],
    risk_task_reliability_assessment: [],
  };

  const defaultTemplateProps = {
    onClose: mockOnClose,
    form: mockTemplateForm,
    setForm: mockSetForm,
    type: 'template' as const,
  };

  const defaultRiskProps = {
    onClose: mockOnClose,
    form: mockRiskForm,
    setForm: mockSetForm,
    type: 'risk' as const,
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Modal Rendering', () => {
    it('renders modal with correct title and structure', () => {
      render(<AddJobModal {...defaultTemplateProps} />);
      
      expect(screen.getByText('Add Associated Job')).toBeInTheDocument();
      expect(screen.getByRole('dialog')).toBeInTheDocument();
      expect(screen.getByTestId('add-jobs-step')).toBeInTheDocument();
    });

    it('renders modal with correct size and backdrop properties', () => {
      render(<AddJobModal {...defaultTemplateProps} />);

      const modal = screen.getByRole('dialog');
      expect(modal).toBeInTheDocument();
      // Modal should be rendered with proper structure
      expect(modal).toHaveAttribute('role', 'dialog');
    });

    it('renders Cancel and Save Changes buttons', () => {
      render(<AddJobModal {...defaultTemplateProps} />);
      
      expect(screen.getByRole('button', { name: 'Cancel' })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: 'Save Changes' })).toBeInTheDocument();
    });

    it('applies correct CSS classes to modal elements', () => {
      render(<AddJobModal {...defaultTemplateProps} />);

      const modalBody = screen.getByTestId('add-jobs-step').closest('.modal-body');
      expect(modalBody).toHaveClass('edit-modal-body');

      const cancelButton = screen.getByRole('button', { name: 'Cancel' });
      expect(cancelButton).toHaveClass('me-2', 'fs-14', 'btn-primary');

      const saveButton = screen.getByRole('button', { name: 'Save Changes' });
      expect(saveButton).toHaveClass('me-2', 'fs-14', 'btn-secondary');
    });
  });

  describe('Props and Type Handling', () => {
    it('defaults to template type when type prop is not provided', () => {
      const propsWithoutType = {
        onClose: mockOnClose,
        form: mockTemplateForm,
        setForm: mockSetForm,
      };
      
      render(<AddJobModal {...propsWithoutType} />);
      
      expect(screen.getByTestId('type-template')).toBeInTheDocument();
    });

    it('handles template type correctly', () => {
      render(<AddJobModal {...defaultTemplateProps} />);
      
      expect(screen.getByTestId('type-template')).toBeInTheDocument();
      expect(screen.getByTestId('is-edit-true')).toBeInTheDocument();
      expect(screen.getByTestId('job-index-0')).toBeInTheDocument();
    });

    it('handles risk type correctly', () => {
      render(<AddJobModal {...defaultRiskProps} />);
      
      expect(screen.getByTestId('type-risk')).toBeInTheDocument();
      expect(screen.getByTestId('is-edit-true')).toBeInTheDocument();
      expect(screen.getByTestId('job-index-0')).toBeInTheDocument();
    });
  });

  describe('Form Initialization', () => {
    it('initializes template form with empty template job', () => {
      render(<AddJobModal {...defaultTemplateProps} />);
      
      const jobStepInput = screen.getByTestId('job-step-input');
      const jobHazardInput = screen.getByTestId('job-hazard-input');
      
      expect(jobStepInput).toHaveValue('');
      expect(jobHazardInput).toHaveValue('');
    });

    it('initializes risk form with empty risk job', () => {
      render(<AddJobModal {...defaultRiskProps} />);
      
      const jobStepInput = screen.getByTestId('job-step-input');
      const jobHazardInput = screen.getByTestId('job-hazard-input');
      
      expect(jobStepInput).toHaveValue('');
      expect(jobHazardInput).toHaveValue('');
    });
  });

  describe('Button States', () => {
    it('disables Save Changes button initially when form is empty', () => {
      render(<AddJobModal {...defaultTemplateProps} />);

      const saveButton = screen.getByRole('button', { name: 'Save Changes' });
      expect(saveButton).toBeDisabled();
    });

    it('enables Save Changes button when form has changes and is valid', async () => {
      render(<AddJobModal {...defaultTemplateProps} />);

      const jobStepInput = screen.getByTestId('job-step-input');
      fireEvent.change(jobStepInput, { target: { value: 'Test Job Step' } });

      await waitFor(() => {
        const saveButton = screen.getByRole('button', { name: 'Save Changes' });
        expect(saveButton).not.toBeDisabled();
      });
    });

    it('Cancel button is always enabled', () => {
      render(<AddJobModal {...defaultTemplateProps} />);

      const cancelButton = screen.getByRole('button', { name: 'Cancel' });
      expect(cancelButton).not.toBeDisabled();
    });
  });

  describe('User Interactions', () => {
    it('calls onClose when Cancel button is clicked', () => {
      render(<AddJobModal {...defaultTemplateProps} />);

      const cancelButton = screen.getByRole('button', { name: 'Cancel' });
      fireEvent.click(cancelButton);

      expect(mockOnClose).toHaveBeenCalled();
      expect(mockSetForm).not.toHaveBeenCalled();
    });

    it('calls onClose when modal is closed', () => {
      render(<AddJobModal {...defaultTemplateProps} />);

      // Test that the modal has the onHide prop set correctly
      // Since we can't easily test backdrop click with React Bootstrap Modal,
      // we'll test that the onClose function is properly passed
      expect(screen.getByRole('dialog')).toBeInTheDocument();
    });

    it('handles form field changes correctly for template type', async () => {
      render(<AddJobModal {...defaultTemplateProps} />);

      const jobStepInput = screen.getByTestId('job-step-input');
      const jobHazardInput = screen.getByTestId('job-hazard-input');

      fireEvent.change(jobStepInput, { target: { value: 'New Job Step' } });
      fireEvent.change(jobHazardInput, { target: { value: 'New Hazard' } });

      expect(jobStepInput).toHaveValue('New Job Step');
      expect(jobHazardInput).toHaveValue('New Hazard');
    });

    it('handles form field changes correctly for risk type', async () => {
      render(<AddJobModal {...defaultRiskProps} />);

      const jobStepInput = screen.getByTestId('job-step-input');
      const jobHazardInput = screen.getByTestId('job-hazard-input');

      fireEvent.change(jobStepInput, { target: { value: 'Risk Job Step' } });
      fireEvent.change(jobHazardInput, { target: { value: 'Risk Hazard' } });

      expect(jobStepInput).toHaveValue('Risk Job Step');
      expect(jobHazardInput).toHaveValue('Risk Hazard');
    });
  });

  describe('Save Functionality', () => {
    it('calls save when validation passes for template type', async () => {
      render(<AddJobModal {...defaultTemplateProps} />);

      const jobStepInput = screen.getByTestId('job-step-input');
      fireEvent.change(jobStepInput, { target: { value: 'Test Job Step' } });

      await waitFor(() => {
        const saveButton = screen.getByRole('button', { name: 'Save Changes' });
        expect(saveButton).not.toBeDisabled();
      });

      const saveButton = screen.getByRole('button', { name: 'Save Changes' });
      fireEvent.click(saveButton);

      expect(mockSetForm).toHaveBeenCalled();
      expect(mockOnClose).toHaveBeenCalled();
    });

    it('saves template job successfully when validation passes', async () => {
      render(<AddJobModal {...defaultTemplateProps} />);

      const jobStepInput = screen.getByTestId('job-step-input');
      fireEvent.change(jobStepInput, { target: { value: 'Test Job Step' } });

      await waitFor(() => {
        const saveButton = screen.getByRole('button', { name: 'Save Changes' });
        expect(saveButton).not.toBeDisabled();
      });

      const saveButton = screen.getByRole('button', { name: 'Save Changes' });
      fireEvent.click(saveButton);

      expect(mockSetForm).toHaveBeenCalledWith(expect.any(Function));
      expect(mockOnClose).toHaveBeenCalledWith();

      // Test the function passed to setForm
      const setFormCall = mockSetForm.mock.calls[0][0];
      const updatedForm = setFormCall(mockTemplateForm);

      expect(updatedForm.template_job).toHaveLength(1);
      expect(updatedForm.template_job[0]).toEqual(expect.objectContaining({
        job_id: 'mock-uuid-1234',
        job_step: 'Test Job Step',
      }));
    });

    it('saves risk job successfully when validation passes', async () => {
      render(<AddJobModal {...defaultRiskProps} />);

      const jobStepInput = screen.getByTestId('job-step-input');
      fireEvent.change(jobStepInput, { target: { value: 'Risk Job Step' } });

      await waitFor(() => {
        const saveButton = screen.getByRole('button', { name: 'Save Changes' });
        expect(saveButton).not.toBeDisabled();
      });

      const saveButton = screen.getByRole('button', { name: 'Save Changes' });
      fireEvent.click(saveButton);

      expect(mockSetForm).toHaveBeenCalledWith(expect.any(Function));
      expect(mockOnClose).toHaveBeenCalledWith();

      // Test the function passed to setForm
      const setFormCall = mockSetForm.mock.calls[0][0];
      const updatedForm = setFormCall(mockRiskForm);

      expect(updatedForm.risk_job).toHaveLength(1);
      expect(updatedForm.risk_job[0]).toEqual(expect.objectContaining({
        job_step: 'Risk Job Step',
      }));
    });

    it('generates UUID for template job when job_id is empty', async () => {
      render(<AddJobModal {...defaultTemplateProps} />);

      const jobStepInput = screen.getByTestId('job-step-input');
      fireEvent.change(jobStepInput, { target: { value: 'Test Job Step' } });

      await waitFor(() => {
        const saveButton = screen.getByRole('button', { name: 'Save Changes' });
        fireEvent.click(saveButton);
      });

      const setFormCall = mockSetForm.mock.calls[0][0];
      const updatedForm = setFormCall(mockTemplateForm);

      expect(updatedForm.template_job[0].job_id).toBe('mock-uuid-1234');
    });

    it('handles empty risk_job array when saving', async () => {
      render(<AddJobModal {...defaultRiskProps} />);

      const jobStepInput = screen.getByTestId('job-step-input');
      fireEvent.change(jobStepInput, { target: { value: 'Test Job Step' } });

      await waitFor(() => {
        const saveButton = screen.getByRole('button', { name: 'Save Changes' });
        fireEvent.click(saveButton);
      });

      // Should call setForm and onClose when saving
      expect(mockSetForm).toHaveBeenCalled();
      expect(mockOnClose).toHaveBeenCalled();
    });

    it('handles empty template_job array when saving', async () => {
      render(<AddJobModal {...defaultTemplateProps} />);

      const jobStepInput = screen.getByTestId('job-step-input');
      fireEvent.change(jobStepInput, { target: { value: 'Test Job Step' } });

      await waitFor(() => {
        const saveButton = screen.getByRole('button', { name: 'Save Changes' });
        fireEvent.click(saveButton);
      });

      // Should call setForm and onClose when saving
      expect(mockSetForm).toHaveBeenCalled();
      expect(mockOnClose).toHaveBeenCalled();
    });
  });

  describe('Form State Management', () => {
    it('tracks changes correctly when multiple fields are filled', async () => {
      render(<AddJobModal {...defaultTemplateProps} />);

      const saveButton = screen.getByRole('button', { name: 'Save Changes' });
      expect(saveButton).toBeDisabled();

      const jobStepInput = screen.getByTestId('job-step-input');
      fireEvent.change(jobStepInput, { target: { value: 'Test Job Step' } });

      await waitFor(() => {
        expect(saveButton).not.toBeDisabled();
      });

      const jobHazardInput = screen.getByTestId('job-hazard-input');
      fireEvent.change(jobHazardInput, { target: { value: 'Test Hazard' } });

      await waitFor(() => {
        expect(saveButton).not.toBeDisabled();
      });
    });

    it('handles validation state changes correctly', async () => {
      const mockValidate = jest.fn(() => true);

      jest.doMock('../../../src/pages/CreateRA/AddJobsStep', () => ({
        AddJobsStep: React.forwardRef(({onValidate}: any, ref: any) => {
          React.useImperativeHandle(ref, () => ({
            validate: mockValidate,
          }));

          React.useEffect(() => {
            onValidate?.(true);
          }, [onValidate]);

          return <div data-testid="add-jobs-step">Mocked AddJobsStep</div>;
        }),
      }));

      render(<AddJobModal {...defaultTemplateProps} />);

      await waitFor(() => {
        // Validation should be called and form should be valid
        expect(mockValidate).toBeDefined();
      });
    });

    it('preserves existing form data when adding new job', async () => {
      const formWithExistingJobs = {
        ...mockTemplateForm,
        template_job: [
          {
            job_id: 'existing-job-1',
            job_step: 'Existing Job Step',
            job_hazard: 'Existing Hazard',
            job_nature_of_risk: '',
            job_existing_control: '',
            job_additional_mitigation: '',
            job_close_out_date: '',
            job_close_out_responsibility_id: '',
            template_job_initial_risk_rating: [],
            template_job_residual_risk_rating: [],
          },
        ],
      };

      render(<AddJobModal {...defaultTemplateProps} form={formWithExistingJobs} />);

      const jobStepInput = screen.getByTestId('job-step-input');
      fireEvent.change(jobStepInput, { target: { value: 'New Job Step' } });

      await waitFor(() => {
        const saveButton = screen.getByRole('button', { name: 'Save Changes' });
        fireEvent.click(saveButton);
      });

      const setFormCall = mockSetForm.mock.calls[0][0];
      const updatedForm = setFormCall(formWithExistingJobs);

      expect(updatedForm.template_job).toHaveLength(2);
      expect(updatedForm.template_job[0]).toEqual(expect.objectContaining({
        job_id: 'existing-job-1',
        job_step: 'Existing Job Step',
      }));
      expect(updatedForm.template_job[1]).toEqual(expect.objectContaining({
        job_id: 'mock-uuid-1234',
        job_step: 'New Job Step',
      }));
    });
  });

  describe('Edge Cases and Error Handling', () => {
    it('handles form with minimal required properties', () => {
      const minimalForm = {
        ...mockTemplateForm,
        template_job: [],
      };

      expect(() => {
        render(<AddJobModal {...defaultTemplateProps} form={minimalForm} />);
      }).not.toThrow();
    });

    it('handles null setForm function', () => {
      const propsWithNullSetForm = {
        ...defaultTemplateProps,
        setForm: null as any,
      };

      expect(() => {
        render(<AddJobModal {...propsWithNullSetForm} />);
      }).not.toThrow();
    });

    it('handles missing onClose function', () => {
      const propsWithoutOnClose = {
        ...defaultTemplateProps,
        onClose: undefined as any,
      };

      expect(() => {
        render(<AddJobModal {...propsWithoutOnClose} />);
      }).not.toThrow();
    });

    it('handles invalid type prop', () => {
      const propsWithInvalidType = {
        ...defaultTemplateProps,
        type: 'invalid' as any,
      };

      expect(() => {
        render(<AddJobModal {...propsWithInvalidType} />);
      }).not.toThrow();
    });

    it('handles form with null job arrays', () => {
      const formWithNullJobs = {
        ...mockTemplateForm,
        template_job: null as any,
      };

      expect(() => {
        render(<AddJobModal {...defaultTemplateProps} form={formWithNullJobs} />);
      }).not.toThrow();
    });

    it('handles form with undefined job arrays', () => {
      const formWithUndefinedJobs = {
        ...mockRiskForm,
        risk_job: undefined as any,
      };

      expect(() => {
        render(<AddJobModal {...defaultRiskProps} form={formWithUndefinedJobs} />);
      }).not.toThrow();
    });
  });

  describe('Integration with AddJobsStep', () => {
    it('passes correct props to AddJobsStep component', () => {
      render(<AddJobModal {...defaultTemplateProps} />);

      expect(screen.getByTestId('add-jobs-step')).toBeInTheDocument();
      expect(screen.getByTestId('job-index-0')).toBeInTheDocument();
      expect(screen.getByTestId('is-edit-true')).toBeInTheDocument();
      expect(screen.getByTestId('type-template')).toBeInTheDocument();
    });

    it('passes correct props to AddJobsStep for risk type', () => {
      render(<AddJobModal {...defaultRiskProps} />);

      expect(screen.getByTestId('add-jobs-step')).toBeInTheDocument();
      expect(screen.getByTestId('job-index-0')).toBeInTheDocument();
      expect(screen.getByTestId('is-edit-true')).toBeInTheDocument();
      expect(screen.getByTestId('type-risk')).toBeInTheDocument();
    });

    it('handles AddJobsStep validation callback correctly', async () => {
      render(<AddJobModal {...defaultTemplateProps} />);

      const jobStepInput = screen.getByTestId('job-step-input');
      fireEvent.change(jobStepInput, { target: { value: 'Test Job Step' } });

      await waitFor(() => {
        const saveButton = screen.getByRole('button', { name: 'Save Changes' });
        expect(saveButton).not.toBeDisabled();
      });
    });

    it('handles AddJobsStep ref correctly', async () => {
      render(<AddJobModal {...defaultTemplateProps} />);

      const jobStepInput = screen.getByTestId('job-step-input');
      fireEvent.change(jobStepInput, { target: { value: 'Test Job Step' } });

      await waitFor(() => {
        const saveButton = screen.getByRole('button', { name: 'Save Changes' });
        fireEvent.click(saveButton);
      });

      // The validate method should have been called through the ref
      expect(mockSetForm).toHaveBeenCalled();
    });
  });

  describe('Accessibility and UX', () => {
    it('has proper ARIA attributes', () => {
      render(<AddJobModal {...defaultTemplateProps} />);

      const modal = screen.getByRole('dialog');
      expect(modal).toBeInTheDocument();

      const modalTitle = screen.getByText('Add Associated Job');
      expect(modalTitle).toBeInTheDocument();
    });

    it('focuses on modal when opened', () => {
      render(<AddJobModal {...defaultTemplateProps} />);

      const modal = screen.getByRole('dialog');
      expect(modal).toBeInTheDocument();
    });

    it('supports keyboard navigation', () => {
      render(<AddJobModal {...defaultTemplateProps} />);

      const cancelButton = screen.getByRole('button', { name: 'Cancel' });
      const saveButton = screen.getByRole('button', { name: 'Save Changes' });

      expect(cancelButton).toBeInTheDocument();
      expect(saveButton).toBeInTheDocument();

      // Both buttons should be focusable
      cancelButton.focus();
      expect(document.activeElement).toBe(cancelButton);

      // Note: Save button might be disabled initially, so we just check it exists
      expect(saveButton).toBeInTheDocument();
    });

    it('displays proper button text and styling', () => {
      render(<AddJobModal {...defaultTemplateProps} />);

      const cancelButton = screen.getByRole('button', { name: 'Cancel' });
      const saveButton = screen.getByRole('button', { name: 'Save Changes' });

      expect(cancelButton).toHaveTextContent('Cancel');
      expect(saveButton).toHaveTextContent('Save Changes');

      expect(cancelButton).toHaveClass('btn-primary');
      expect(saveButton).toHaveClass('btn-secondary');
    });
  });

  describe('Performance and Memory', () => {
    it('cleans up properly when unmounted', () => {
      const { unmount } = render(<AddJobModal {...defaultTemplateProps} />);

      expect(() => {
        unmount();
      }).not.toThrow();
    });

    it('handles rapid state changes without errors', async () => {
      render(<AddJobModal {...defaultTemplateProps} />);

      const jobStepInput = screen.getByTestId('job-step-input');

      // Rapidly change the input value
      for (let i = 0; i < 10; i++) {
        fireEvent.change(jobStepInput, { target: { value: `Test ${i}` } });
      }

      await waitFor(() => {
        expect(jobStepInput).toHaveValue('Test 9');
      });
    });

    it('handles modal rerendering correctly', () => {
      const { rerender } = render(<AddJobModal {...defaultTemplateProps} />);

      expect(screen.getByRole('dialog')).toBeInTheDocument();
      expect(screen.getByTestId('type-template')).toBeInTheDocument();

      // Rerender with different props
      const newTemplateProps = {
        ...defaultTemplateProps,
        form: {
          ...mockTemplateForm,
          task_requiring_ra: 'Updated Task',
        },
      };

      rerender(<AddJobModal {...newTemplateProps} />);

      expect(screen.getByRole('dialog')).toBeInTheDocument();
      expect(screen.getByTestId('type-template')).toBeInTheDocument();
    });
  });
});
