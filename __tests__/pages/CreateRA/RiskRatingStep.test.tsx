import React from 'react';
import {render, screen, fireEvent, waitFor} from '@testing-library/react';
import '@testing-library/jest-dom';
import {RiskRatingStep} from '../../../src/pages/CreateRA/RiskRatingStep';
import {TemplateForm} from '../../../src/types/template';
import {TemplateFormStatus} from '../../../src/enums';

// Mock dependencies
jest.mock('../../../src/context', () => ({
  useDataStoreContext: jest.fn(),
}));

jest.mock('../../../src/components/InputComponent', () => ({
  InputComponent: ({
    label,
    name,
    value,
    onChange,
    onBlur,
    placeholder,
    type,
    form,
    error,
  }: any) => (
    <div data-testid={`input-${name}`}>
      <label htmlFor={name}>{label}</label>
      <textarea
        id={name}
        name={name}
        value={form?.[name] || value || ''}
        onChange={onChange}
        onBlur={onBlur}
        placeholder={placeholder}
        data-testid={`input-field-${name}`}
      />
      {error && <div data-testid={`error-${name}`}>{error}</div>}
    </div>
  ),
}));

jest.mock('../../../src/components/FormCheckRadio', () => ({
  __esModule: true,
  default: ({checked, onChange, name, value, label, id, disabled}: any) => (
    <div data-testid={`radio-${id}`}>
      <input
        type="radio"
        name={name}
        value={value}
        checked={checked}
        onChange={onChange}
        disabled={disabled}
        data-testid={`radio-input-${id}`}
      />
      <label htmlFor={id}>{label}</label>
    </div>
  ),
}));

jest.mock('../../../src/utils/svgIcons', () => ({
  InfoIcon: (props: any) => (
    <span data-testid="info-icon" {...props}>
      ℹ
    </span>
  ),
}));

jest.mock('moment', () => {
  const moment = jest.requireActual('moment');
  return {
    __esModule: true,
    default: () => ({
      format: jest.fn(() => '18 Jun 2025'),
    }),
  };
});

describe('RiskRatingStep Component', () => {
  const mockSetForm = jest.fn();
  const mockOnValidate = jest.fn();

  const mockTaskReliabilityAssessList = [
    {
      id: 1,
      name: 'Is the task routine and familiar to the crew?',
      options: ['Yes', 'No', 'Partially'],
    },
    {
      id: 2,
      name: 'Are the crew adequately trained and competent?',
      options: ['Yes', 'No'],
    },
    {
      id: 3,
      name: 'Are appropriate procedures available and followed?',
      options: ['Yes', 'No', 'N/A'],
    },
  ];

  const defaultForm: TemplateForm = {
    task_requiring_ra: 'Test Risk Assessment Task',
    task_duration: '5',
    task_alternative_consideration: 'Alternative',
    task_rejection_reason: 'Reason',
    worst_case_scenario: 'Test worst case scenario',
    recovery_measures: 'Test recovery measures',
    status: TemplateFormStatus.DRAFT,
    parameters: [],
    template_category: {category_id: []},
    template_hazard: {is_other: false, value: '', hazard_id: []},
    template_job: [],
    template_task_reliability_assessment: [
      {
        task_reliability_assessment_id: 1,
        task_reliability_assessment_answer: 'Yes',
        condition: 'Test condition message',
      },
      {
        task_reliability_assessment_id: 2,
        task_reliability_assessment_answer: 'No',
        condition: '',
      },
    ],
    template_keyword: [],
  };

  const mockUseDataStoreContext = {
    dataStore: {
      taskReliabilityAssessList: mockTaskReliabilityAssessList,
    },
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (
      require('../../../src/context').useDataStoreContext as jest.Mock
    ).mockReturnValue(mockUseDataStoreContext);
  });

  const renderRiskRatingStep = (
    form = defaultForm,
    setForm = mockSetForm,
    onValidate = mockOnValidate,
    disableHeader = false,
  ) => {
    const ref = React.createRef<any>();
    render(
      <RiskRatingStep
        ref={ref}
        form={form}
        setForm={setForm}
        onValidate={onValidate}
        disableHeader={disableHeader}
      />,
    );
    return {ref};
  };

  describe('Component Rendering', () => {
    it('renders the component with header by default', () => {
      renderRiskRatingStep();

      expect(screen.getByText('Test Risk Assessment Task')).toBeInTheDocument();
      expect(
        screen.getByText('Date of Risk Assessment: 18 Jun 2025'),
      ).toBeInTheDocument();
      expect(screen.getByText('Overall Risk Rating')).toBeInTheDocument();
      expect(screen.getByText('High')).toBeInTheDocument(); // Risk rating should be displayed
    });

    it('renders without header when disableHeader is true', () => {
      renderRiskRatingStep(defaultForm, mockSetForm, mockOnValidate, true);

      expect(
        screen.queryByText('Test Risk Assessment Task'),
      ).not.toBeInTheDocument();
      expect(
        screen.queryByText('Date of Risk Assessment: 18 Jun 2025'),
      ).not.toBeInTheDocument();
      expect(screen.queryByText('Overall Risk Rating')).not.toBeInTheDocument();
    });

    it('renders worst case scenario input', () => {
      renderRiskRatingStep();

      expect(screen.getByText('Worst Case Scenario')).toBeInTheDocument();
      expect(
        screen.getByTestId('input-field-worst_case_scenario'),
      ).toBeInTheDocument();
    });

    it('renders recovery measures input', () => {
      renderRiskRatingStep();

      expect(screen.getByText('Recovery Measures')).toBeInTheDocument();
      expect(
        screen.getByTestId('input-field-recovery_measures'),
      ).toBeInTheDocument();
    });

    it('renders task reliability assessment section', () => {
      renderRiskRatingStep();

      expect(
        screen.getByText('Task Reliability Assessment'),
      ).toBeInTheDocument();
      expect(
        screen.getByText('(1) Is the task routine and familiar to the crew?'),
      ).toBeInTheDocument();
      expect(
        screen.getByText('(2) Are the crew adequately trained and competent?'),
      ).toBeInTheDocument();
      expect(
        screen.getByText(
          '(3) Are appropriate procedures available and followed?',
        ),
      ).toBeInTheDocument();
    });

    it('renders warning alert', () => {
      renderRiskRatingStep();

      expect(
        screen.getByText(
          /Considerable efforts must be made to implement measures/,
        ),
      ).toBeInTheDocument();
    });
  });

  describe('Risk Rating Calculation', () => {
    it('displays Medium risk rating when no assessments', () => {
      const formWithoutAssessments = {
        ...defaultForm,
        template_task_reliability_assessment: [],
      };

      renderRiskRatingStep(formWithoutAssessments);

      expect(screen.getByText('Medium')).toBeInTheDocument();
    });

    it('displays Medium risk rating when all answers are Yes', () => {
      const formWithAllYes = {
        ...defaultForm,
        template_task_reliability_assessment: [
          {
            task_reliability_assessment_id: 1,
            task_reliability_assessment_answer: 'Yes',
            condition: '',
          },
          {
            task_reliability_assessment_id: 2,
            task_reliability_assessment_answer: 'Yes',
            condition: '',
          },
        ],
      };

      renderRiskRatingStep(formWithAllYes);

      expect(screen.getByText('Medium')).toBeInTheDocument();
    });

    it('displays High risk rating when all answers are No', () => {
      const formWithAllNo = {
        ...defaultForm,
        template_task_reliability_assessment: [
          {
            task_reliability_assessment_id: 1,
            task_reliability_assessment_answer: 'No',
            condition: '',
          },
          {
            task_reliability_assessment_id: 2,
            task_reliability_assessment_answer: 'No',
            condition: '',
          },
        ],
      };

      renderRiskRatingStep(formWithAllNo);

      expect(screen.getByText('High')).toBeInTheDocument();
    });

    it('displays High risk rating when answers are mixed with No', () => {
      renderRiskRatingStep(); // Uses defaultForm with mixed Yes/No answers

      expect(screen.getByText('High')).toBeInTheDocument();
    });
  });

  describe('Form Field Updates', () => {
    it('updates worst case scenario field', () => {
      renderRiskRatingStep();

      const worstCaseInput = screen.getByTestId(
        'input-field-worst_case_scenario',
      );
      fireEvent.change(worstCaseInput, {
        target: {
          name: 'worst_case_scenario',
          value: 'Updated worst case scenario',
        },
      });

      expect(mockSetForm).toHaveBeenCalledWith(expect.any(Function));
    });

    it('updates recovery measures field', () => {
      renderRiskRatingStep();

      const recoveryInput = screen.getByTestId('input-field-recovery_measures');
      fireEvent.change(recoveryInput, {
        target: {name: 'recovery_measures', value: 'Updated recovery measures'},
      });

      expect(mockSetForm).toHaveBeenCalledWith(expect.any(Function));
    });

    it('handles blur events for validation', () => {
      renderRiskRatingStep();

      const worstCaseInput = screen.getByTestId(
        'input-field-worst_case_scenario',
      );
      fireEvent.blur(worstCaseInput, {target: {name: 'worst_case_scenario'}});

      expect(mockOnValidate).toHaveBeenCalled();
    });

    it('displays form field values correctly', () => {
      renderRiskRatingStep();

      expect(
        screen.getByDisplayValue('Test worst case scenario'),
      ).toBeInTheDocument();
      expect(
        screen.getByDisplayValue('Test recovery measures'),
      ).toBeInTheDocument();
    });
  });

  describe('Task Reliability Assessment', () => {
    it('renders radio options for each question', () => {
      renderRiskRatingStep();

      // Check first question has Yes, No, Partially options
      expect(screen.getByTestId('radio-question-1-Yes')).toBeInTheDocument();
      expect(screen.getByTestId('radio-question-1-No')).toBeInTheDocument();
      expect(
        screen.getByTestId('radio-question-1-Partially'),
      ).toBeInTheDocument();

      // Check second question has Yes, No options
      expect(screen.getByTestId('radio-question-2-Yes')).toBeInTheDocument();
      expect(screen.getByTestId('radio-question-2-No')).toBeInTheDocument();
    });

    it('shows selected answers correctly', () => {
      renderRiskRatingStep();

      // First question should have Yes selected (from defaultForm)
      const yesRadio1 = screen.getByTestId('radio-input-question-1-Yes');
      expect(yesRadio1).toBeChecked();

      // Second question should have No selected (from defaultForm)
      const noRadio2 = screen.getByTestId('radio-input-question-2-No');
      expect(noRadio2).toBeChecked();
    });

    it('handles radio button changes', () => {
      renderRiskRatingStep();

      const partiallyRadio = screen.getByTestId(
        'radio-input-question-1-Partially',
      );
      fireEvent.click(partiallyRadio);

      expect(mockSetForm).toHaveBeenCalledWith(expect.any(Function));
    });

    it('shows condition input when Yes is selected', () => {
      renderRiskRatingStep();

      // First question has Yes selected, so condition input should be visible
      expect(
        screen.getByPlaceholderText(
          'State the "Stand by Units" or "Redundancy"',
        ),
      ).toBeInTheDocument();
    });

    it('handles condition input changes', () => {
      renderRiskRatingStep();

      const conditionInput = screen.getByPlaceholderText(
        'State the "Stand by Units" or "Redundancy"',
      );
      fireEvent.change(conditionInput, {target: {value: 'Updated condition'}});

      expect(mockSetForm).toHaveBeenCalledWith(expect.any(Function));
    });

    it('hides condition input when No is selected', () => {
      const formWithNoAnswers = {
        ...defaultForm,
        template_task_reliability_assessment: [
          {
            task_reliability_assessment_id: 1,
            task_reliability_assessment_answer: 'No',
            condition: '',
          },
          {
            task_reliability_assessment_id: 2,
            task_reliability_assessment_answer: 'No',
            condition: '',
          },
        ],
      };

      renderRiskRatingStep(formWithNoAnswers);

      // Should not show condition input when No is selected
      expect(
        screen.queryByPlaceholderText(
          'State the "Stand by Units" or "Redundancy"',
        ),
      ).not.toBeInTheDocument();
    });
  });

  describe('Validation', () => {
    it('validates required fields are empty', () => {
      const emptyForm = {
        ...defaultForm,
        worst_case_scenario: '',
        recovery_measures: '',
        template_task_reliability_assessment: [],
      };

      const {ref} = renderRiskRatingStep(emptyForm);

      const isValid = ref.current?.validate();
      expect(isValid).toBe(false);
      expect(mockOnValidate).toHaveBeenCalledWith(false);
    });

    it('validates successfully when all fields are filled', () => {
      const {ref} = renderRiskRatingStep();

      const isValid = ref.current?.validate();
      expect(isValid).toBe(true);
      expect(mockOnValidate).toHaveBeenCalledWith(true);
    });

    it('validates task reliability assessment answers', () => {
      const formWithIncompleteAssessments = {
        ...defaultForm,
        template_task_reliability_assessment: [
          {
            task_reliability_assessment_id: 1,
            task_reliability_assessment_answer: '',
            condition: '',
          },
        ],
      };

      const {ref} = renderRiskRatingStep(formWithIncompleteAssessments);

      const isValid = ref.current?.validate();
      expect(isValid).toBe(false);
      expect(mockOnValidate).toHaveBeenCalledWith(false);
    });
  });

  describe('Edge Cases', () => {
    it('handles missing task reliability assessment list gracefully', () => {
      const mockContextWithoutAssessments = {
        dataStore: {
          taskReliabilityAssessList: [],
        },
      };

      (
        require('../../../src/context').useDataStoreContext as jest.Mock
      ).mockReturnValue(mockContextWithoutAssessments);

      const formWithoutAssessments = {
        ...defaultForm,
        template_task_reliability_assessment: [],
      };

      renderRiskRatingStep(formWithoutAssessments);

      expect(
        screen.getByText('Task Reliability Assessment'),
      ).toBeInTheDocument();
      expect(screen.getByText('Medium')).toBeInTheDocument(); // Should default to Medium
    });

    it('handles undefined task reliability assessment list', () => {
      const mockContextWithUndefined = {
        dataStore: {
          taskReliabilityAssessList: [],
        },
      };

      (
        require('../../../src/context').useDataStoreContext as jest.Mock
      ).mockReturnValue(mockContextWithUndefined);

      const formWithoutAssessments = {
        ...defaultForm,
        template_task_reliability_assessment: [],
      };

      renderRiskRatingStep(formWithoutAssessments);

      expect(
        screen.getByText('Task Reliability Assessment'),
      ).toBeInTheDocument();
      expect(screen.getByText('Medium')).toBeInTheDocument();
    });

    it('handles form with empty task name', () => {
      const formWithEmptyTask = {
        ...defaultForm,
        task_requiring_ra: '',
      };

      renderRiskRatingStep(formWithEmptyTask);

      // When task name is empty, it should just render empty div
      expect(screen.queryByText('ssss')).not.toBeInTheDocument();
    });

    it('handles assessment answer updates correctly', () => {
      renderRiskRatingStep();

      const noRadio = screen.getByTestId('radio-input-question-1-No');
      fireEvent.click(noRadio);

      expect(mockSetForm).toHaveBeenCalledWith(expect.any(Function));

      // Test the function passed to setForm
      const setFormCall =
        mockSetForm.mock.calls[mockSetForm.mock.calls.length - 1][0];
      const newForm = setFormCall(defaultForm);

      const updatedAssessment =
        newForm.template_task_reliability_assessment.find(
          (item: any) => item.task_reliability_assessment_id === 1,
        );
      expect(updatedAssessment?.task_reliability_assessment_answer).toBe('No');
    });

    it('handles new assessment creation when not exists', () => {
      const formWithoutAssessments = {
        ...defaultForm,
        template_task_reliability_assessment: [],
      };

      renderRiskRatingStep(formWithoutAssessments);

      const yesRadio = screen.getByTestId('radio-input-question-1-Yes');
      fireEvent.click(yesRadio);

      expect(mockSetForm).toHaveBeenCalledWith(expect.any(Function));

      // Test the function passed to setForm
      const setFormCall =
        mockSetForm.mock.calls[mockSetForm.mock.calls.length - 1][0];
      const newForm = setFormCall(formWithoutAssessments);

      expect(newForm.template_task_reliability_assessment).toHaveLength(1);
      expect(newForm.template_task_reliability_assessment[0]).toEqual({
        task_reliability_assessment_id: 1,
        task_reliability_assessment_answer: 'Yes',
        condition: '',
      });
    });

    it('handles condition updates for existing assessments', () => {
      renderRiskRatingStep();

      const conditionInput = screen.getByPlaceholderText(
        'State the "Stand by Units" or "Redundancy"',
      );
      fireEvent.change(conditionInput, {target: {value: 'New condition'}});

      expect(mockSetForm).toHaveBeenCalledWith(expect.any(Function));

      // Test the function passed to setForm
      const setFormCall =
        mockSetForm.mock.calls[mockSetForm.mock.calls.length - 1][0];
      const newForm = setFormCall(defaultForm);

      const updatedAssessment =
        newForm.template_task_reliability_assessment.find(
          (item: any) => item.task_reliability_assessment_id === 1,
        );
      expect(updatedAssessment?.condition).toBe('New condition');
    });
  });

  describe('Risk Rating Colors', () => {
    it('displays High risk rating correctly', () => {
      const formWithHighRisk = {
        ...defaultForm,
        template_task_reliability_assessment: [
          {
            task_reliability_assessment_id: 1,
            task_reliability_assessment_answer: 'No',
            condition: '',
          },
        ],
      };

      renderRiskRatingStep(formWithHighRisk);

      expect(screen.getByText('High')).toBeInTheDocument();
    });

    it('displays High risk rating correctly for mixed answers', () => {
      renderRiskRatingStep(); // Uses mixed answers with No = High risk

      expect(screen.getByText('High')).toBeInTheDocument();
    });

    it('displays Medium risk rating correctly for all Yes answers', () => {
      const formWithMediumRisk = {
        ...defaultForm,
        template_task_reliability_assessment: [
          {
            task_reliability_assessment_id: 1,
            task_reliability_assessment_answer: 'Yes',
            condition: '',
          },
          {
            task_reliability_assessment_id: 2,
            task_reliability_assessment_answer: 'Yes',
            condition: '',
          },
        ],
      };

      renderRiskRatingStep(formWithMediumRisk);

      expect(screen.getByText('Medium')).toBeInTheDocument();
    });
  });

  describe('Component Ref Methods', () => {
    it('exposes validate method through ref', () => {
      const {ref} = renderRiskRatingStep();

      expect(ref.current).toBeDefined();
      expect(typeof ref.current?.validate).toBe('function');
    });

    it('validate method returns boolean', () => {
      const {ref} = renderRiskRatingStep();

      const result = ref.current?.validate();
      expect(typeof result).toBe('boolean');
    });
  });
});
