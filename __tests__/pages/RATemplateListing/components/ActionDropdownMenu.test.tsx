import React from 'react';
import {render, screen} from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import '@testing-library/jest-dom';
import {
  ActionDropdownMenu,
  ActionDropdownMenuProps,
} from '../../../../src/pages/RATemplateListing/components/ActionDropdownMenu';
import {Template} from '../../../../src/types/template';
import {BasicUserDetails} from '../../../../src/types/user';

// Mock dependencies
jest.mock('../../../../src/components/icons', () => ({
  ThreeDotsMenuIcon: function MockThreeDotsMenuIcon() {
    return <div data-testid="three-dots-icon">⋯</div>;
  },
}));

jest.mock(
  '../../../../src/pages/RATemplateListing/components/ArchiveTemplateModal',
  () => {
    return function MockArchiveTemplateModal({
      templateId,
      trigger,
      templateName,
      riskCategories,
      hazardCategories,
      keywords,
      createdOn,
      userName,
      onSuccess,
    }: {
      templateId: number;
      trigger: React.ReactElement;
      templateName: string;
      riskCategories: any;
      hazardCategories: any;
      keywords: any;
      createdOn: string;
      userName: string;
      onSuccess?: () => void;
    }) {
      return (
        <div data-testid="archive-template-modal">
          <div data-testid="modal-template-id">{templateId}</div>
          <div data-testid="modal-template-name">{templateName}</div>
          <div data-testid="modal-user-name">{userName}</div>
          <div data-testid="modal-created-on">{createdOn}</div>
          <button data-testid="modal-trigger" onClick={() => onSuccess?.()}>
            {trigger}
          </button>
        </div>
      );
    };
  },
);

jest.mock('../../../../src/components/PreviewTemplateModal', () => ({
  PreviewTemplateModal: function MockPreviewTemplateModal({
    onClose,
    id,
    canUseTemplate,
  }: {
    onClose: () => void;
    id: number;
    canUseTemplate: boolean;
  }) {
    return (
      <div data-testid="preview-template-modal">
        <div data-testid="preview-modal-id">{id}</div>
        <div data-testid="preview-modal-can-use">{canUseTemplate.toString()}</div>
        <button data-testid="preview-modal-close" onClick={onClose}>
          Close Preview
        </button>
      </div>
    );
  },
}));

const mockUseDataStoreContext = jest.fn();

jest.mock('../../../../src/context', () => ({
  useDataStoreContext: () => mockUseDataStoreContext(),
}));

// Mock console.log to avoid test output noise
const originalConsoleLog = console.log;
beforeAll(() => {
  console.log = jest.fn();
});

afterAll(() => {
  console.log = originalConsoleLog;
});

// Helper function to render component with user event setup
const renderWithUser = (component: React.ReactElement) => {
  const user = userEvent.setup();
  return {
    user,
    ...render(component),
  };
};

// Test wrapper component to test internal state changes
const TestWrapper = ({children}: {children: React.ReactNode}) => {
  return <div data-testid="test-wrapper">{children}</div>;
};



describe('ActionDropdownMenu', () => {
  const mockTemplateData: Pick<
    Template,
    | 'id'
    | 'task_requiring_ra'
    | 'template_category'
    | 'template_hazards'
    | 'template_keywords'
    | 'createdAt'
    | 'created_at'
  > = {
    id: 1,
    task_requiring_ra: 'Test Task',
    template_category: [
      {
        id: 1,
        template_id: 1,
        category_id: 1,
        category_is_other: false,
        status: 1,
        category: {id: 1, name: 'Safety', type: 1},
        value: null,
        created_by: 'user1',
        updated_by: null,
        deleted_at: null,
        deleted_by: null,
        createdAt: '2023-01-01T00:00:00Z',
        updatedAt: '2023-01-01T00:00:00Z',
      },
    ],
    template_hazards: [
      {
        id: 1,
        template_id: 1,
        hazard_id: 1,
        hazard_category_is_other: false,
        status: 1,
        value: null,
        hazard_detail: {id: 1, name: 'Chemical', type: 1},
        created_by: 'user1',
        updated_by: null,
        deleted_at: null,
        deleted_by: null,
        createdAt: '2023-01-01T00:00:00Z',
        updatedAt: '2023-01-01T00:00:00Z',
      },
    ],
    template_keywords: [
      {
        id: 1,
        template_id: 1,
        name: 'maintenance',
        status: 1,
        created_by: 'user1',
        updated_by: null,
        deleted_at: null,
        deleted_by: null,
        createdAt: '2023-01-01T00:00:00Z',
        updatedAt: '2023-01-01T00:00:00Z',
      },
    ],
    createdAt: '2023-01-01T00:00:00Z',
    created_at: '2023-01-01T00:00:00Z',
  };

  const mockUserDetails: Partial<
    Pick<BasicUserDetails, 'full_name' | 'email'>
  > = {
    full_name: 'John Doe',
    email: '<EMAIL>',
  };

  const defaultProps: ActionDropdownMenuProps = {
    data: mockTemplateData,
    userDetails: mockUserDetails,
  };

  beforeEach(() => {
    mockUseDataStoreContext.mockReturnValue({
      roleConfig: {
        riskAssessment: {
          hasPermision: true,
        },
      },
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Component Rendering', () => {
    it('should render the dropdown menu with three dots icon', () => {
      const {container} = render(<ActionDropdownMenu {...defaultProps} />);

      expect(screen.getByTestId('three-dots-icon')).toBeInTheDocument();

      // Check for dropdown toggle element
      const dropdownToggle = container.querySelector('.dropdown-toggle');
      expect(dropdownToggle).toBeInTheDocument();
    });

    it('should render with correct CSS classes for horizontal alignment by default', () => {
      const {container} = render(<ActionDropdownMenu {...defaultProps} />);

      const dropdown = container.querySelector('.ra-three-dots-dropdown');
      expect(dropdown).toBeInTheDocument();
      expect(dropdown).toHaveClass(
        'd-flex',
        'align-items-center',
        'justify-content-center',
      );

      const toggle = container.querySelector('.dropdown-toggle-no-caret');
      expect(toggle).toBeInTheDocument();
      expect(toggle).not.toHaveClass('ra-menu-vertical');
    });

    it('should render with vertical alignment class when menuAlign is vertical', () => {
      const {container} = render(
        <ActionDropdownMenu {...defaultProps} menuAlign="vertical" />,
      );

      const toggle = container.querySelector('.dropdown-toggle-no-caret');
      expect(toggle).toHaveClass('ra-menu-vertical');
    });

    it('should render dropdown structure with correct classes', () => {
      const {container} = render(<ActionDropdownMenu {...defaultProps} />);

      // Check for dropdown container
      const dropdown = container.querySelector('.dropdown');
      expect(dropdown).toBeInTheDocument();

      // Check for dropdown toggle
      const dropdownToggle = container.querySelector('.dropdown-toggle');
      expect(dropdownToggle).toBeInTheDocument();
    });
  });

  describe('Menu Items', () => {
    it('should render dropdown with proper structure', () => {
      const {container} = render(<ActionDropdownMenu {...defaultProps} />);

      // Check that dropdown exists
      const dropdown = container.querySelector('.dropdown');
      expect(dropdown).toBeInTheDocument();

      // Check that dropdown toggle exists
      const dropdownToggle = container.querySelector('.dropdown-toggle');
      expect(dropdownToggle).toBeInTheDocument();
    });

    it('should render component correctly when user has permission', () => {
      mockUseDataStoreContext.mockReturnValue({
        roleConfig: {
          riskAssessment: {
            hasPermision: true,
          },
        },
      });

      const {container} = render(<ActionDropdownMenu {...defaultProps} />);

      // Check that component renders without errors when user has permission
      const dropdown = container.querySelector('.dropdown');
      expect(dropdown).toBeInTheDocument();
    });

    it('should not render Archive modal when user does not have permission', () => {
      mockUseDataStoreContext.mockReturnValue({
        roleConfig: {
          riskAssessment: {
            hasPermision: false,
          },
        },
      });

      render(<ActionDropdownMenu {...defaultProps} />);

      expect(
        screen.queryByTestId('archive-template-modal'),
      ).not.toBeInTheDocument();
    });

    it('should contain dropdown menu structure for menu items', () => {
      const {container} = render(<ActionDropdownMenu {...defaultProps} />);

      // Bootstrap dropdown menus are not rendered in DOM until opened
      // But we can check that the component structure is correct
      const dropdown = container.querySelector('.dropdown');
      expect(dropdown).toBeInTheDocument();

      // Check for the presence of dropdown classes that indicate menu capability
      expect(dropdown).toHaveClass('dropdown');
    });

    it('should render dropdown component structure', () => {
      const {container} = render(<ActionDropdownMenu {...defaultProps} />);

      // Check for dropdown container
      const dropdown = container.querySelector('.dropdown');
      expect(dropdown).toBeInTheDocument();
      expect(dropdown).toHaveClass('ra-three-dots-dropdown');
    });

    it('should render dropdown toggle', () => {
      const {container} = render(<ActionDropdownMenu {...defaultProps} />);

      // Check for dropdown toggle
      const dropdownToggle = container.querySelector('.dropdown-toggle');
      expect(dropdownToggle).toBeInTheDocument();
      expect(dropdownToggle).toHaveClass('dropdown-toggle-no-caret');
    });

    it('should render three dots icon', () => {
      render(<ActionDropdownMenu {...defaultProps} />);

      // Check that three dots icon is rendered
      expect(screen.getByTestId('three-dots-icon')).toBeInTheDocument();
    });
  });

  describe('ArchiveTemplateModal Integration', () => {
    it('should render component structure correctly regardless of modal visibility', () => {
      mockUseDataStoreContext.mockReturnValue({
        roleConfig: {
          riskAssessment: {
            hasPermision: true,
          },
        },
      });

      const {container} = render(<ActionDropdownMenu {...defaultProps} />);

      // Check that the component renders without errors
      const dropdown = container.querySelector('.dropdown');
      expect(dropdown).toBeInTheDocument();
    });

    it('should render component when user has permission', () => {
      mockUseDataStoreContext.mockReturnValue({
        roleConfig: {
          riskAssessment: {
            hasPermision: true,
          },
        },
      });

      const {container} = render(<ActionDropdownMenu {...defaultProps} />);

      // Check that component renders without errors when user has permission
      const dropdown = container.querySelector('.dropdown');
      expect(dropdown).toBeInTheDocument();
    });

    it('should render component correctly with different permission states', () => {
      // Test with permission
      mockUseDataStoreContext.mockReturnValue({
        roleConfig: {
          riskAssessment: {
            hasPermision: true,
          },
        },
      });

      const {rerender} = render(<ActionDropdownMenu {...defaultProps} />);

      // Component should render without errors
      expect(screen.getByTestId('three-dots-icon')).toBeInTheDocument();

      // Test without permission
      mockUseDataStoreContext.mockReturnValue({
        roleConfig: {
          riskAssessment: {
            hasPermision: false,
          },
        },
      });

      rerender(<ActionDropdownMenu {...defaultProps} />);

      // Component should still render without errors
      expect(screen.getByTestId('three-dots-icon')).toBeInTheDocument();
    });

    it('should render component with onSuccess callback when user has permission', () => {
      mockUseDataStoreContext.mockReturnValue({
        roleConfig: {
          riskAssessment: {
            hasPermision: true,
          },
        },
      });

      const mockOnSuccess = jest.fn();
      render(<ActionDropdownMenu {...defaultProps} onSuccess={mockOnSuccess} />);

      // Component should render without errors when onSuccess is provided
      expect(screen.getByTestId('three-dots-icon')).toBeInTheDocument();
    });

    it('should handle different data configurations without errors', () => {
      mockUseDataStoreContext.mockReturnValue({
        roleConfig: {
          riskAssessment: {
            hasPermision: true,
          },
        },
      });

      const dataWithoutCreatedAt = {
        ...mockTemplateData,
        createdAt: undefined as any,
        created_at: '2023-02-01T00:00:00Z',
      };

      expect(() => {
        render(
          <ActionDropdownMenu {...defaultProps} data={dataWithoutCreatedAt} />,
        );
      }).not.toThrow();
    });

    it('should handle data with both created_at and createdAt fields', () => {
      mockUseDataStoreContext.mockReturnValue({
        roleConfig: {
          riskAssessment: {
            hasPermision: true,
          },
        },
      });

      const dataWithBothDates = {
        ...mockTemplateData,
        createdAt: '2023-01-01T00:00:00Z',
        created_at: '2023-02-01T00:00:00Z',
      };

      expect(() => {
        render(
          <ActionDropdownMenu {...defaultProps} data={dataWithBothDates} />,
        );
      }).not.toThrow();
    });

    it('should handle data with only createdAt field', () => {
      mockUseDataStoreContext.mockReturnValue({
        roleConfig: {
          riskAssessment: {
            hasPermision: true,
          },
        },
      });

      const dataWithOnlyCreatedAt = {
        ...mockTemplateData,
        created_at: undefined as any,
        createdAt: '2023-01-01T00:00:00Z',
      };

      expect(() => {
        render(
          <ActionDropdownMenu {...defaultProps} data={dataWithOnlyCreatedAt} />,
        );
      }).not.toThrow();
    });

    it('should handle different user details configurations', () => {
      mockUseDataStoreContext.mockReturnValue({
        roleConfig: {
          riskAssessment: {
            hasPermision: true,
          },
        },
      });

      const userDetailsWithoutName = {
        email: '<EMAIL>',
      };

      expect(() => {
        render(
          <ActionDropdownMenu
            {...defaultProps}
            userDetails={userDetailsWithoutName}
          />,
        );
      }).not.toThrow();
    });

    it('should handle empty user details gracefully', () => {
      mockUseDataStoreContext.mockReturnValue({
        roleConfig: {
          riskAssessment: {
            hasPermision: true,
          },
        },
      });

      const emptyUserDetails = {};

      expect(() => {
        render(
          <ActionDropdownMenu
            {...defaultProps}
            userDetails={emptyUserDetails}
          />,
        );
      }).not.toThrow();
    });

    it('should handle onSuccess callback prop correctly', () => {
      mockUseDataStoreContext.mockReturnValue({
        roleConfig: {
          riskAssessment: {
            hasPermision: true,
          },
        },
      });

      const mockOnSuccess = jest.fn();

      expect(() => {
        render(
          <ActionDropdownMenu {...defaultProps} onSuccess={mockOnSuccess} />,
        );
      }).not.toThrow();
    });

    it('should render without onSuccess callback', () => {
      mockUseDataStoreContext.mockReturnValue({
        roleConfig: {
          riskAssessment: {
            hasPermision: true,
          },
        },
      });

      expect(() => {
        render(<ActionDropdownMenu {...defaultProps} />);
      }).not.toThrow();
    });

    it('should render component with correct data when user has permission', () => {
      mockUseDataStoreContext.mockReturnValue({
        roleConfig: {
          riskAssessment: {
            hasPermision: true,
          },
        },
      });

      render(<ActionDropdownMenu {...defaultProps} />);

      // Component should render without errors with the provided data
      expect(screen.getByTestId('three-dots-icon')).toBeInTheDocument();
    });

    it('should handle user details with email only', () => {
      mockUseDataStoreContext.mockReturnValue({
        roleConfig: {
          riskAssessment: {
            hasPermision: true,
          },
        },
      });

      const userDetailsWithEmailOnly = {
        email: '<EMAIL>',
      };

      expect(() => {
        render(
          <ActionDropdownMenu
            {...defaultProps}
            userDetails={userDetailsWithEmailOnly}
          />,
        );
      }).not.toThrow();
    });

    it('should handle empty user details', () => {
      mockUseDataStoreContext.mockReturnValue({
        roleConfig: {
          riskAssessment: {
            hasPermision: true,
          },
        },
      });

      const emptyUserDetails = {};

      expect(() => {
        render(
          <ActionDropdownMenu {...defaultProps} userDetails={emptyUserDetails} />,
        );
      }).not.toThrow();
    });
  });

  describe('Props Handling', () => {
    it('should handle menuAlign prop correctly', () => {
      const {rerender, container} = render(
        <ActionDropdownMenu {...defaultProps} menuAlign="horizontal" />,
      );

      let toggle = container.querySelector('.dropdown-toggle-no-caret');
      expect(toggle).not.toHaveClass('ra-menu-vertical');

      rerender(<ActionDropdownMenu {...defaultProps} menuAlign="vertical" />);

      toggle = container.querySelector('.dropdown-toggle-no-caret');
      expect(toggle).toHaveClass('ra-menu-vertical');
    });

    it('should default to horizontal alignment when menuAlign is not provided', () => {
      const {container} = render(<ActionDropdownMenu {...defaultProps} />);

      const toggle = container.querySelector('.dropdown-toggle-no-caret');
      expect(toggle).not.toHaveClass('ra-menu-vertical');
    });
  });

  describe('Component Structure and Rendering', () => {
    it('should not show PreviewTemplateModal initially', () => {
      render(<ActionDropdownMenu {...defaultProps} />);

      expect(
        screen.queryByTestId('preview-template-modal'),
      ).not.toBeInTheDocument();
    });

    it('should render component structure correctly', () => {
      const {container} = render(<ActionDropdownMenu {...defaultProps} />);

      // Check that the dropdown has the necessary structure
      const dropdown = container.querySelector('.dropdown');
      expect(dropdown).toBeInTheDocument();
      expect(dropdown).toHaveClass('dropdown');

      const toggle = container.querySelector('.dropdown-toggle');
      expect(toggle).toBeInTheDocument();
      expect(toggle).toHaveAttribute('aria-expanded');
    });

    it('should render component without errors', () => {
      // Test that the component renders without throwing errors
      expect(() => {
        render(<ActionDropdownMenu {...defaultProps} />);
      }).not.toThrow();
    });

    it('should render component structure without errors', () => {
      const {container} = render(<ActionDropdownMenu {...defaultProps} />);

      // Check that the component renders the basic structure
      const dropdown = container.querySelector('.dropdown');
      expect(dropdown).toBeInTheDocument();
      expect(screen.getByTestId('three-dots-icon')).toBeInTheDocument();
    });

    it('should render component when user has archive permission', () => {
      mockUseDataStoreContext.mockReturnValue({
        roleConfig: {
          riskAssessment: {
            hasPermision: true,
          },
        },
      });

      render(<ActionDropdownMenu {...defaultProps} />);

      // Component should render without errors when user has permission
      expect(screen.getByTestId('three-dots-icon')).toBeInTheDocument();
    });

    it('should render component when user lacks archive permission', () => {
      mockUseDataStoreContext.mockReturnValue({
        roleConfig: {
          riskAssessment: {
            hasPermision: false,
          },
        },
      });

      render(<ActionDropdownMenu {...defaultProps} />);

      // Component should still render without errors when user lacks permission
      expect(screen.getByTestId('three-dots-icon')).toBeInTheDocument();
    });
  });

  describe('Edge Cases', () => {
    it('should handle missing template data gracefully', () => {
      const minimalData = {
        id: 1,
        task_requiring_ra: '',
        template_category: [],
        template_hazards: [],
        template_keywords: [],
        createdAt: '',
        created_at: '',
      };

      expect(() => {
        render(<ActionDropdownMenu {...defaultProps} data={minimalData} />);
      }).not.toThrow();
    });

    it('should handle null/undefined roleConfig gracefully', () => {
      mockUseDataStoreContext.mockReturnValue({
        roleConfig: {
          riskAssessment: {
            hasPermision: undefined,
          },
        },
      });

      render(<ActionDropdownMenu {...defaultProps} />);

      // Should not render archive modal when permission is undefined/falsy
      expect(
        screen.queryByTestId('archive-template-modal'),
      ).not.toBeInTheDocument();
    });

    it('should handle missing context gracefully', () => {
      mockUseDataStoreContext.mockReturnValue({});

      expect(() => {
        render(<ActionDropdownMenu {...defaultProps} />);
      }).toThrow(); // This should throw because roleConfig is required
    });
  });

  describe('Accessibility', () => {
    it('should have proper ARIA attributes for dropdown', () => {
      const {container} = render(<ActionDropdownMenu {...defaultProps} />);

      const dropdownToggle = container.querySelector('.dropdown-toggle');
      expect(dropdownToggle).toBeInTheDocument();
      expect(dropdownToggle).toHaveAttribute('aria-expanded');
    });

    it('should have focusable dropdown toggle', () => {
      const {container} = render(<ActionDropdownMenu {...defaultProps} />);

      const dropdownToggle = container.querySelector('.dropdown-toggle');
      expect(dropdownToggle).toBeInTheDocument();

      // Check that element can receive focus (tabindex or focusable element)
      expect(dropdownToggle).toBeInTheDocument();
    });

    it('should have proper dropdown structure for screen readers', () => {
      const {container} = render(<ActionDropdownMenu {...defaultProps} />);

      const dropdown = container.querySelector('.dropdown');
      expect(dropdown).toBeInTheDocument();

      // Check that dropdown toggle has proper attributes
      const dropdownToggle = container.querySelector('.dropdown-toggle');
      expect(dropdownToggle).toHaveAttribute('aria-expanded');
      expect(dropdownToggle).toHaveAttribute('id');
    });

    it('should have proper CSS classes for accessibility', () => {
      const {container} = render(<ActionDropdownMenu {...defaultProps} />);

      const dropdown = container.querySelector('.dropdown');
      expect(dropdown).toHaveClass('dropdown');

      const toggle = container.querySelector('.dropdown-toggle');
      expect(toggle).toHaveClass('dropdown-toggle');
    });
  });
});
