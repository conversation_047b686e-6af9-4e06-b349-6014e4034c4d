import {
  cleanObject,
  parseDate,
  getDateRangeFilters,
} from '../../src/utils/common';
import moment from 'moment';

describe('cleanObject', () => {
  it('removes keys with null or undefined values', () => {
    const input = {a: null, b: undefined, c: 'value'};
    const result = cleanObject(input);
    expect(result).toEqual({c: 'value'});
  });

  it('removes keys with empty arrays', () => {
    const input = {a: [], b: [1, 2]};
    const result = cleanObject(input);
    expect(result).toEqual({b: [1, 2]});
  });

  it('removes nested empty objects recursively', () => {
    const input = {
      a: {
        b: {},
        c: {
          d: null,
        },
      },
      e: 'value',
    };
    const result = cleanObject(input);
    expect(result).toEqual({e: 'value'});
  });

  it('keeps nested objects with non-empty keys', () => {
    const input = {
      a: {
        b: 'value',
      },
    };
    const result = cleanObject(input);
    expect(result).toEqual(input);
  });

  it('returns empty object if all values are empty or null', () => {
    const input = {
      a: null,
      b: [],
      c: {
        d: [],
      },
    };
    const result = cleanObject(input);
    expect(result).toEqual({});
  });
});

describe('parseDate', () => {
  it('returns formatted date string for valid date input', () => {
    const date = new Date('2023-01-15T00:00:00Z');
    const formatted = parseDate(date);
    expect(formatted).toBe(moment(date).format('DD MMM YYYY'));
  });

  it('returns undefined for invalid date string', () => {
    expect(parseDate('invalid-date')).toBeUndefined();
  });

  it('returns undefined when date is null or undefined', () => {
    expect(parseDate(null)).toBeUndefined();
    expect(parseDate(undefined)).toBeUndefined();
  });

  it('formats date with custom format string', () => {
    const date = '2023-06-17';
    const formatted = parseDate(date, 'YYYY/MM/DD');
    expect(formatted).toBe(moment(date).format('YYYY/MM/DD'));
  });
});

describe('getDateRangeFilters', () => {
  it('returns empty object if no range is provided', () => {
    expect(getDateRangeFilters('date')).toEqual({});
    expect(getDateRangeFilters('date', null)).toEqual({});
    expect(getDateRangeFilters('date', [null, null])).toEqual({});
  });

  it('returns only start_date filter if only start date is provided', () => {
    const result = getDateRangeFilters('created', ['2023-01-01', null]);
    expect(result).toEqual({'created[start_date]': '2023-01-01'});
  });

  it('returns only end_date filter if only end date is provided', () => {
    const result = getDateRangeFilters('updated', [null, '2023-12-31']);
    expect(result).toEqual({'updated[end_date]': '2023-12-31'});
  });

  it('returns both start_date and end_date filters if both provided', () => {
    const result = getDateRangeFilters('date', ['2023-01-01', '2023-12-31']);
    expect(result).toEqual({
      'date[start_date]': '2023-01-01',
      'date[end_date]': '2023-12-31',
    });
  });
});
