import {extractErrorMessage} from '../../src/utils/error';
import {AxiosError} from 'axios';

describe('extractErrorMessage', () => {
  it('returns default message when error is falsy or has no message', () => {
    expect(extractErrorMessage(null)).toBe(
      'Something went wrong. Please try again',
    );
    expect(extractErrorMessage(undefined)).toBe(
      'Something went wrong. Please try again',
    );
    expect(extractErrorMessage({})).toBe(
      'Something went wrong. Please try again',
    );
  });

  it('returns error.message if present', () => {
    const error = {message: 'Custom error message'};
    expect(extractErrorMessage(error)).toBe('Custom error message');
  });

  it('returns axios error message if isAxiosError is true and message exists', () => {
    const error = {
      isAxiosError: true,
      message: 'Axios error message',
    };
    expect(extractErrorMessage(error)).toBe('Axios error message');
  });

  it('returns nested axios response.data.message if present', () => {
    const error = {
      isAxiosError: true,
      message: 'Axios error message',
      response: {
        data: {
          message: 'Nested response error message',
        },
      },
    };
    expect(extractErrorMessage(error)).toBe('Nested response error message');
  });

  it('falls back to axios error message if response.data.message is missing', () => {
    const error = {
      isAxiosError: true,
      message: 'Axios error message',
      response: {
        data: {},
      },
    };
    expect(extractErrorMessage(error)).toBe('Axios error message');
  });

  it('handles error that is actually an AxiosError instance', () => {
    const axiosError = new Error('Axios error') as AxiosError;
    axiosError.isAxiosError = true;
    axiosError.response = {data: {message: 'Axios response error'}} as any;

    expect(extractErrorMessage(axiosError)).toBe('Axios response error');
  });
});
