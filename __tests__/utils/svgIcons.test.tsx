import React from 'react';
import {render, screen} from '@testing-library/react';
import {
  SortIcon,
  RoundCheckFilled,
  CheckFilled,
  CheckUnFilled,
  CommentIcon,
  InfoIcon,
  CalendarIcon,
  DeleteJobIcon,
  JobCardArrowUpIcon,
  JobCardArrowDownIcon,
  EditFormDetailIcon,
  ActionMenuIcon,
  RadioUncheckedIcon,
  RadioCheckedIcon,
  JobAlertIcon,
  CrewIcon,
  ExclaimationIcon,
  ProfileIcon,
  CrossIcon,
} from '../../src/utils/svgIcons';

describe('SVG Icons', () => {
  test('SortIcon renders correctly', () => {
    render(<SortIcon data-testid="sort-icon" />);
    const icon = screen.getByTestId('sort-icon');
    expect(icon).toBeInTheDocument();
    expect(icon).toHaveAttribute('viewBox', '0 0 24 24');
  });

  test('RoundCheckFilled renders correctly', () => {
    const { container } = render(<RoundCheckFilled />);
    const icon = container.querySelector('svg');
    expect(icon).toBeInTheDocument();
    expect(icon).toHaveAttribute('viewBox', '0 0 24 24');
    expect(icon).toHaveAttribute('width', '24');
    expect(icon).toHaveAttribute('height', '24');
    expect(icon?.querySelector('circle')).toHaveAttribute('fill', '#0DA666');
    expect(icon?.querySelector('path')).toHaveAttribute('fill', 'white');
  });

  test('CheckFilled renders correctly', () => {
    render(<CheckFilled data-testid="check-filled" />);
    const icon = screen.getByTestId('check-filled');
    expect(icon).toBeInTheDocument();
    expect(icon).toHaveAttribute('viewBox', '0 0 20 20');
    expect(icon.querySelector('rect')).toHaveAttribute('fill', '#0091B8');
  });

  test('CheckUnFilled renders correctly', () => {
    render(<CheckUnFilled data-testid="check-unfilled" />);
    const icon = screen.getByTestId('check-unfilled');
    expect(icon).toBeInTheDocument();
    expect(icon.querySelector('rect')).toHaveAttribute('fill', 'white');
    expect(icon.querySelector('rect')).toHaveAttribute('stroke', '#CCCCCC');
  });

  test('CommentIcon renders correctly', () => {
    render(<CommentIcon data-testid="comment-icon" />);
    const icon = screen.getByTestId('comment-icon');
    expect(icon).toBeInTheDocument();
    expect(icon).toHaveAttribute('viewBox', '0 0 20 20');
  });

  test('InfoIcon renders correctly', () => {
    render(<InfoIcon data-testid="info-icon" />);
    const icon = screen.getByTestId('info-icon');
    expect(icon).toBeInTheDocument();
    expect(icon).toHaveAttribute('width', '16');
    expect(icon).toHaveAttribute('height', '16');
  });

  test('CalendarIcon renders correctly', () => {
    render(<CalendarIcon data-testid="calendar-icon" />);
    const icon = screen.getByTestId('calendar-icon');
    expect(icon).toBeInTheDocument();
    expect(icon).toHaveAttribute('viewBox', '0 0 14 14');
  });

  test('DeleteJobIcon renders correctly', () => {
    render(<DeleteJobIcon data-testid="delete-job-icon" />);
    const icon = screen.getByTestId('delete-job-icon');
    expect(icon).toBeInTheDocument();
    expect(icon.querySelector('path')).toHaveAttribute('fill', '#1F4A70');
  });

  test('JobCardArrowUpIcon renders correctly', () => {
    render(<JobCardArrowUpIcon data-testid="arrow-up-icon" />);
    const icon = screen.getByTestId('arrow-up-icon');
    expect(icon).toBeInTheDocument();
    expect(icon.querySelector('path')).toHaveAttribute('stroke', '#1F4A70');
  });

  test('JobCardArrowDownIcon renders correctly', () => {
    render(<JobCardArrowDownIcon data-testid="arrow-down-icon" />);
    const icon = screen.getByTestId('arrow-down-icon');
    expect(icon).toBeInTheDocument();
    expect(icon.querySelector('path')).toHaveAttribute('stroke', '#1F4A70');
  });

  test('EditFormDetailIcon renders correctly', () => {
    render(<EditFormDetailIcon data-testid="edit-form-icon" />);
    const icon = screen.getByTestId('edit-form-icon');
    expect(icon).toBeInTheDocument();
    expect(icon.querySelector('path')).toHaveAttribute('fill', '#1F4A70');
  });

  test('ActionMenuIcon renders correctly', () => {
    render(<ActionMenuIcon data-testid="action-menu-icon" />);
    const icon = screen.getByTestId('action-menu-icon');
    expect(icon).toBeInTheDocument();
    expect(icon.querySelectorAll('path')).toHaveLength(3);
  });

  test('RadioUncheckedIcon renders correctly', () => {
    render(<RadioUncheckedIcon data-testid="radio-unchecked-icon" />);
    const icon = screen.getByTestId('radio-unchecked-icon');
    expect(icon).toBeInTheDocument();
    expect(icon.querySelector('rect')).toHaveAttribute('fill', 'white');
  });

  test('RadioCheckedIcon renders correctly', () => {
    render(<RadioCheckedIcon data-testid="radio-checked-icon" />);
    const icon = screen.getByTestId('radio-checked-icon');
    expect(icon).toBeInTheDocument();
    expect(icon.querySelector('rect')).toHaveAttribute('fill', '#0091B8');
  });

  test('JobAlertIcon renders correctly', () => {
    render(<JobAlertIcon data-testid="job-alert-icon" />);
    const icon = screen.getByTestId('job-alert-icon');
    expect(icon).toBeInTheDocument();
    expect(icon).toHaveAttribute('viewBox', '0 0 32 32');
    expect(icon).toHaveAttribute('width', '32');
    expect(icon).toHaveAttribute('height', '32');
    expect(icon.querySelector('path')).toHaveAttribute('fill', '#FFF9E8');
  });

  test('CrewIcon renders correctly', () => {
    const { container } = render(<CrewIcon />);
    const icon = container.querySelector('svg');
    expect(icon).toBeInTheDocument();
    expect(icon).toHaveAttribute('viewBox', '0 0 73 72');
    expect(icon).toHaveAttribute('width', '73');
    expect(icon).toHaveAttribute('height', '72');
    expect(icon?.querySelector('path')).toHaveAttribute('fill', '#1F4A70');
  });

  test('ExclaimationIcon renders correctly', () => {
    const { container } = render(<ExclaimationIcon />);
    const icon = container.querySelector('svg');
    expect(icon).toBeInTheDocument();
    expect(icon).toHaveAttribute('viewBox', '0 0 48 49');
    expect(icon).toHaveAttribute('width', '48');
    expect(icon).toHaveAttribute('height', '49');
    expect(icon?.querySelector('path')).toHaveAttribute('stroke', '#1F4A70');
  });

  test('ProfileIcon renders correctly', () => {
    const { container } = render(<ProfileIcon />);
    const icon = container.querySelector('svg');
    expect(icon).toBeInTheDocument();
    expect(icon).toHaveAttribute('viewBox', '0 0 302 56');
    expect(icon).toHaveAttribute('width', '302');
    expect(icon).toHaveAttribute('height', '56');
    expect(icon?.querySelector('circle')).toHaveAttribute('fill', '#E5F4F8');
    expect(icon?.querySelector('path')).toHaveAttribute('fill', '#1F4A70');
  });

  test('CrossIcon renders correctly', () => {
    const { container } = render(<CrossIcon />);
    const icon = container.querySelector('svg');
    expect(icon).toBeInTheDocument();
    expect(icon).toHaveAttribute('viewBox', '0 0 20 20');
    expect(icon).toHaveAttribute('width', '20');
    expect(icon).toHaveAttribute('height', '20');
    expect(icon?.querySelector('path')).toBeDefined();
  });

  test('Icons accept and pass through props', () => {
    render(
      <SortIcon
        data-testid="props-test-icon"
        className="test-class"
        width="50"
        height="50"
      />,
    );
    const icon = screen.getByTestId('props-test-icon');
    expect(icon).toHaveClass('test-class');
    expect(icon).toHaveAttribute('width', '50');
    expect(icon).toHaveAttribute('height', '50');
  });

  test('Icons that support props accept custom props and spread them correctly', () => {
    const customProps = {
      'data-testid': 'custom-props-test',
      className: 'custom-class',
      style: { color: 'red' },
      onClick: jest.fn(),
    };

    // Test icons that properly spread props (like JobAlertIcon)
    render(<JobAlertIcon {...customProps} />);
    const jobAlertIcon = screen.getByTestId('custom-props-test');
    expect(jobAlertIcon).toHaveClass('custom-class');
    expect(jobAlertIcon).toHaveStyle('color: red');
  });

  test('Icons render without props', () => {
    // Test that icons can render without any props
    const { container } = render(
      <div>
        <JobAlertIcon />
        <CrewIcon />
        <ExclaimationIcon />
        <ProfileIcon />
        <CrossIcon />
      </div>
    );

    const svgElements = container.querySelectorAll('svg');
    expect(svgElements).toHaveLength(5);
    svgElements.forEach(svg => {
      expect(svg).toBeInTheDocument();
    });
  });

  test('Icons maintain their default dimensions when no size props provided', () => {
    const { container } = render(
      <div>
        <JobAlertIcon />
        <CrewIcon />
        <ExclaimationIcon />
        <ProfileIcon />
        <CrossIcon />
      </div>
    );

    const svgElements = container.querySelectorAll('svg');
    expect(svgElements[0]).toHaveAttribute('width', '32'); // JobAlertIcon
    expect(svgElements[1]).toHaveAttribute('width', '73'); // CrewIcon
    expect(svgElements[2]).toHaveAttribute('width', '48'); // ExclaimationIcon
    expect(svgElements[3]).toHaveAttribute('width', '302'); // ProfileIcon
    expect(svgElements[4]).toHaveAttribute('width', '20'); // CrossIcon
  });

  test('Icons with props support accept custom dimensions', () => {
    // Test icons that properly spread props can accept custom dimensions
    render(
      <div>
        <JobAlertIcon data-testid="job-alert-custom" width="50" height="50" />
        <SortIcon data-testid="sort-custom" width="100" height="100" />
      </div>
    );

    expect(screen.getByTestId('job-alert-custom')).toHaveAttribute('width', '50');
    expect(screen.getByTestId('job-alert-custom')).toHaveAttribute('height', '50');
    expect(screen.getByTestId('sort-custom')).toHaveAttribute('width', '100');
    expect(screen.getByTestId('sort-custom')).toHaveAttribute('height', '100');
  });
});
