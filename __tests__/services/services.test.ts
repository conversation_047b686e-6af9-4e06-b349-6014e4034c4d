import {
  getRiskCategoryList,
  getHazardsList,
  getRiskParameterType,
  getMainRiskParameterType,
  getTaskReliabilityAssessList,
  generateQueryParams,
  GET_VESSEL_USER_DETAILS,
  getTemplateList,
  getTemplateUserList,
} from '../../src/services/services';
import httpService from '../../src/services/http-service';
import {
  TemplateListResponse,
  TemplateUserResponse,
} from '../../src/types/template';

// Mock the http-service
jest.mock('../../src/services/http-service');
const mockHttpService = httpService as jest.Mocked<typeof httpService>;

// Mock environment variables
const originalEnv = process.env;

describe('Services', () => {
  let mockAxiosClient: any;

  beforeEach(() => {
    // Reset all mocks
    jest.clearAllMocks();

    // Setup mock axios client
    mockAxiosClient = {
      get: jest.fn(),
    };

    mockHttpService.getAxiosClient.mockReturnValue(mockAxiosClient);
    mockHttpService.cancelPreviousRequest.mockReturnValue(
      new AbortController().signal,
    );

    // Setup environment variables
    process.env = {
      ...originalEnv,
      API_RISK_ASSESSMENT_URL: 'https://api.test.com',
    };
  });

  afterEach(() => {
    process.env = originalEnv;
  });

  describe('getRiskCategoryList', () => {
    it('should fetch risk categories successfully', async () => {
      const mockData = [
        {id: 1, name: 'Category 1'},
        {id: 2, name: 'Category 2'},
      ];
      mockAxiosClient.get.mockResolvedValue({data: mockData});

      const result = await getRiskCategoryList();

      expect(mockHttpService.getAxiosClient).toHaveBeenCalled();
      expect(mockAxiosClient.get).toHaveBeenCalledWith(
        'https://api.test.com/categories',
      );
      expect(result).toEqual(mockData);
    });

    it('should handle API errors', async () => {
      const mockError = new Error('API Error');
      mockAxiosClient.get.mockRejectedValue(mockError);

      await expect(getRiskCategoryList()).rejects.toThrow('API Error');
    });
  });

  describe('getHazardsList', () => {
    it('should fetch hazards list successfully', async () => {
      const mockData = [
        {id: 1, name: 'Hazard 1'},
        {id: 2, name: 'Hazard 2'},
      ];
      mockAxiosClient.get.mockResolvedValue({data: mockData});

      const result = await getHazardsList();

      expect(mockHttpService.getAxiosClient).toHaveBeenCalled();
      expect(mockAxiosClient.get).toHaveBeenCalledWith(
        'https://api.test.com/hazards',
      );
      expect(result).toEqual(mockData);
    });

    it('should handle API errors', async () => {
      const mockError = new Error('Network Error');
      mockAxiosClient.get.mockRejectedValue(mockError);

      await expect(getHazardsList()).rejects.toThrow('Network Error');
    });
  });

  describe('getRiskParameterType', () => {
    it('should fetch risk parameter types successfully', async () => {
      const mockData = [
        {id: 1, type: 'Parameter 1'},
        {id: 2, type: 'Parameter 2'},
      ];
      mockAxiosClient.get.mockResolvedValue({data: mockData});

      const result = await getRiskParameterType();

      expect(mockHttpService.getAxiosClient).toHaveBeenCalled();
      expect(mockAxiosClient.get).toHaveBeenCalledWith(
        'https://api.test.com/parameters',
      );
      expect(result).toEqual(mockData);
    });

    it('should handle API errors', async () => {
      const mockError = new Error('Server Error');
      mockAxiosClient.get.mockRejectedValue(mockError);

      await expect(getRiskParameterType()).rejects.toThrow('Server Error');
    });
  });

  describe('getMainRiskParameterType', () => {
    it('should fetch main risk parameter types without filter', async () => {
      const mockData = [
        {id: 1, type: 'Main Parameter 1'},
        {id: 2, type: 'Main Parameter 2'},
      ];
      mockAxiosClient.get.mockResolvedValue({data: mockData});

      const result = await getMainRiskParameterType();

      expect(mockHttpService.getAxiosClient).toHaveBeenCalled();
      expect(mockAxiosClient.get).toHaveBeenCalledWith(
        'https://api.test.com/parameter-types',
        {params: undefined},
      );
      expect(result).toEqual(mockData);
    });

    it('should fetch main risk parameter types with filter for risk rating', async () => {
      const mockData = [{id: 1, type: 'Required Parameter 1'}];
      mockAxiosClient.get.mockResolvedValue({data: mockData});

      const result = await getMainRiskParameterType(true);

      expect(mockHttpService.getAxiosClient).toHaveBeenCalled();
      expect(mockAxiosClient.get).toHaveBeenCalledWith(
        'https://api.test.com/parameter-types',
        {params: {is_required_for_risk_rating: true}},
      );
      expect(result).toEqual(mockData);
    });

    it('should handle API errors', async () => {
      const mockError = new Error('Parameter Error');
      mockAxiosClient.get.mockRejectedValue(mockError);

      await expect(getMainRiskParameterType()).rejects.toThrow(
        'Parameter Error',
      );
    });
  });

  describe('getTaskReliabilityAssessList', () => {
    it('should fetch task reliability assessment list successfully', async () => {
      const mockData = [
        {id: 1, assessment: 'Assessment 1'},
        {id: 2, assessment: 'Assessment 2'},
      ];
      mockAxiosClient.get.mockResolvedValue({data: mockData});

      const result = await getTaskReliabilityAssessList();

      expect(mockHttpService.getAxiosClient).toHaveBeenCalled();
      expect(mockAxiosClient.get).toHaveBeenCalledWith(
        'https://api.test.com/task-reliability-assessments',
      );
      expect(result).toEqual(mockData);
    });

    it('should handle API errors', async () => {
      const mockError = new Error('Assessment Error');
      mockAxiosClient.get.mockRejectedValue(mockError);

      await expect(getTaskReliabilityAssessList()).rejects.toThrow(
        'Assessment Error',
      );
    });
  });

  describe('generateQueryParams', () => {
    it('should generate query params for single value', () => {
      const result = generateQueryParams('key', ['value1']);
      expect(result).toBe('key=value1');
    });

    it('should generate query params for multiple values', () => {
      const result = generateQueryParams('group', ['TechD', 'Admin', 'User']);
      expect(result).toBe('group=TechD&group=Admin&group=User');
    });

    it('should handle empty array', () => {
      const result = generateQueryParams('key', []);
      expect(result).toBe('');
    });

    it('should encode special characters', () => {
      const result = generateQueryParams('search', [
        'test value',
        'special@chars',
      ]);
      expect(result).toBe('search=test%20value&search=special%40chars');
    });

    it('should handle values with spaces and special characters', () => {
      const result = generateQueryParams('category', [
        'Risk & Safety',
        'Health + Wellness',
      ]);
      expect(result).toBe(
        'category=Risk%20%26%20Safety&category=Health%20%2B%20Wellness',
      );
    });
  });

  describe('GET_VESSEL_USER_DETAILS', () => {
    it('should generate correct URL with default parameters', () => {
      const result = GET_VESSEL_USER_DETAILS();
      expect(result).toBe(
        '/keycloak-admin/users?returnAttribute=ship_party_id&group=TechD',
      );
    });

    it('should generate correct URL with custom key and single id', () => {
      const result = GET_VESSEL_USER_DETAILS('role', ['Admin']);
      expect(result).toBe(
        '/keycloak-admin/users?returnAttribute=ship_party_id&role=Admin',
      );
    });

    it('should generate correct URL with custom key and multiple ids', () => {
      const result = GET_VESSEL_USER_DETAILS('department', [
        'Engineering',
        'Safety',
        'Operations',
      ]);
      expect(result).toBe(
        '/keycloak-admin/users?returnAttribute=ship_party_id&department=Engineering&department=Safety&department=Operations',
      );
    });

    it('should handle empty ids array', () => {
      const result = GET_VESSEL_USER_DETAILS('group', []);
      expect(result).toBe(
        '/keycloak-admin/users?returnAttribute=ship_party_id&',
      );
    });
  });

  describe('getTemplateList', () => {
    it('should fetch template list successfully with basic params', async () => {
      const mockResponse: TemplateListResponse = {
        message: 'Success',
        result: {
          data: [
            {
              id: 1,
              task_requiring_ra: 'Test Task',
              task_duration: '60',
              task_alternative_consideration: 'Test consideration',
              task_rejection_reason: '',
              worst_case_scenario: 'Test scenario',
              recovery_measures: 'Test measures',
              status: 1,
              created_by: 'user1',
              updated_by: 'user2',
              deleted_at: null,
              deleted_by: null,
              createdAt: '2023-01-01T00:00:00Z',
              updatedAt: '2023-01-02T00:00:00Z',
              created_at: '2023-01-01T00:00:00Z',
              updated_at: '2023-01-02T00:00:00Z',
              template_category: [],
              template_hazards: [],
              template_job: [],
              template_task_reliability_assessment: [],
              template_keywords: [],
            },
          ],
          pagination: {
            totalItems: 1,
            totalPages: 1,
            page: 1,
            pageSize: 10,
          },
          userDetails: [],
        },
      };

      mockAxiosClient.get.mockResolvedValue({data: mockResponse});

      const result = await getTemplateList({page: 1, limit: 10});

      expect(mockHttpService.cancelPreviousRequest).toHaveBeenCalledWith(
        'template-list',
      );
      expect(mockHttpService.getAxiosClient).toHaveBeenCalled();
      expect(mockAxiosClient.get).toHaveBeenCalledWith(
        'https://api.test.com/templates',
        expect.objectContaining({
          params: {page: 1, limit: 10},
          signal: expect.any(AbortSignal),
        }),
      );
      expect(result).toEqual(mockResponse.result);
    });

    it('should handle API errors', async () => {
      const mockError = new Error('Template fetch error');
      mockAxiosClient.get.mockRejectedValue(mockError);

      await expect(getTemplateList({page: 1})).rejects.toThrow(
        'Template fetch error',
      );
    });
  });

  describe('getTemplateUserList', () => {
    it('should fetch template user list successfully', async () => {
      const mockResponse: TemplateUserResponse = {
        message: 'Success',
        result: [
          {
            userId: 'user1',
            email: '<EMAIL>',
            full_name: 'User One',
            designation: 'Engineer',
          },
        ],
      };

      mockAxiosClient.get.mockResolvedValue({data: mockResponse});

      const result = await getTemplateUserList();

      expect(mockHttpService.getAxiosClient).toHaveBeenCalled();
      expect(mockAxiosClient.get).toHaveBeenCalledWith(
        'https://api.test.com/templates/users',
      );
      expect(result).toEqual(mockResponse.result);
    });

    it('should handle API errors', async () => {
      const mockError = new Error('User list fetch error');
      mockAxiosClient.get.mockRejectedValue(mockError);

      await expect(getTemplateUserList()).rejects.toThrow(
        'User list fetch error',
      );
    });
  });

  describe('Uncovered service functions', () => {
    let mockAxiosClient: any;
    let abortSignal: AbortSignal;
    beforeEach(() => {
      jest.clearAllMocks();
      abortSignal = new AbortController().signal;
      mockAxiosClient = {
        get: jest.fn(),
        post: jest.fn(),
        patch: jest.fn(),
        delete: jest.fn(),
      };
      mockHttpService.getAxiosClient.mockReturnValue(mockAxiosClient);
      mockHttpService.cancelPreviousRequest.mockReturnValue(abortSignal);
      process.env = {
        ...originalEnv,
        API_RISK_ASSESSMENT_URL: 'https://api.test.com',
      };
    });

    it('markTemplateAsArchived should PATCH and return data', async () => {
      const mockData = { success: true };
      mockAxiosClient.patch.mockResolvedValue({ data: mockData });
      const result = await require('../../src/services/services').markTemplateAsArchived(123);
      expect(mockAxiosClient.patch).toHaveBeenCalledWith('https://api.test.com/templates/123/inactive');
      expect(result).toEqual(mockData);
    });

    it('getMostlyUsedTemplates should GET and return result', async () => {
      const mockResult = [{ id: 1 }];
      mockAxiosClient.get.mockResolvedValue({ data: { result: mockResult } });
      const result = await require('../../src/services/services').getMostlyUsedTemplates();
      expect(mockAxiosClient.get).toHaveBeenCalledWith('https://api.test.com/templates/top?maxCount=4');
      expect(result).toEqual(mockResult);
    });

    it('getRAList should GET with params and signal', async () => {
      const mockResult = { data: [], pagination: {} };
      mockAxiosClient.get.mockResolvedValue({ data: { result: mockResult } });
      const params = { page: 1, limit: 2 };
      const result = await require('../../src/services/services').getRAList(params);
      expect(mockAxiosClient.get).toHaveBeenCalledWith('https://api.test.com/risks', expect.objectContaining({ params, signal: abortSignal }));
      expect(result).toEqual(mockResult);
    });

    it('getRAStringOptions should GET and return data', async () => {
      const mockData = [{ value: 'A' }];
      mockAxiosClient.get.mockResolvedValue({ data: mockData });
      const result = await require('../../src/services/services').getRAStringOptions('vessel_category');
      expect(mockAxiosClient.get).toHaveBeenCalledWith('https://api.test.com/risks/options/vessel_category');
      expect(result).toEqual(mockData);
    });

    it('getTemplateById should GET and return data', async () => {
      const mockData = { id: 'abc' };
      mockAxiosClient.get.mockResolvedValue({ data: mockData });
      const result = await require('../../src/services/services').getTemplateById('abc');
      expect(mockAxiosClient.get).toHaveBeenCalledWith('https://api.test.com/templates/abc');
      expect(result).toEqual(mockData);
    });

    it('deleteTemplateById should DELETE and return data', async () => {
      const mockData = { deleted: true };
      mockAxiosClient.delete.mockResolvedValue({ data: mockData });
      const result = await require('../../src/services/services').deleteTemplateById(5);
      expect(mockAxiosClient.delete).toHaveBeenCalledWith('https://api.test.com/templates/5');
      expect(result).toEqual(mockData);
    });

    it('deleteRiskById should DELETE and return data', async () => {
      const mockData = { deleted: true };
      mockAxiosClient.delete.mockResolvedValue({ data: mockData });
      const result = await require('../../src/services/services').deleteRiskById(7);
      expect(mockAxiosClient.delete).toHaveBeenCalledWith('https://api.test.com/risks/7');
      expect(result).toEqual(mockData);
    });

    it('createNewTemplate should POST and return data', async () => {
      const mockData = { id: 1 };
      const payload = { foo: 'bar' };
      mockAxiosClient.post.mockResolvedValue({ data: mockData });
      const result = await require('../../src/services/services').createNewTemplate(payload);
      expect(mockAxiosClient.post).toHaveBeenCalledWith('https://api.test.com/templates', payload);
      expect(result).toEqual(mockData);
    });

    it('updateSavedTemplate should PATCH and return data', async () => {
      const mockData = { updated: true };
      const payload = { foo: 'bar' };
      mockAxiosClient.patch.mockResolvedValue({ data: mockData });
      const result = await require('../../src/services/services').updateSavedTemplate(9, payload);
      expect(mockAxiosClient.patch).toHaveBeenCalledWith('https://api.test.com/templates/9', payload);
      expect(result).toEqual(mockData);
    });

    it('getVesselsList should GET and return data', async () => {
      const mockData = [{ id: 1 }];
      mockAxiosClient.get.mockResolvedValue({ data: mockData });
      const result = await require('../../src/services/services').getVesselsList();
      expect(mockAxiosClient.get).toHaveBeenCalledWith('https://api.test.com/vessel-ownership');
      expect(result).toEqual(mockData);
    });

    it('getOfficesList should GET and return data', async () => {
      const mockData = [{ id: 1 }];
      mockAxiosClient.get.mockResolvedValue({ data: mockData });
      const result = await require('../../src/services/services').getOfficesList();
      expect(mockAxiosClient.get).toHaveBeenCalledWith('https://api.test.com/reporting-office');
      expect(result).toEqual(mockData);
    });

    // Error cases for uncovered functions
    it('should throw on error for markTemplateAsArchived', async () => {
      mockAxiosClient.patch.mockRejectedValue(new Error('fail'));
      await expect(require('../../src/services/services').markTemplateAsArchived(1)).rejects.toThrow('fail');
    });
    it('should throw on error for getMostlyUsedTemplates', async () => {
      mockAxiosClient.get.mockRejectedValue(new Error('fail'));
      await expect(require('../../src/services/services').getMostlyUsedTemplates()).rejects.toThrow('fail');
    });
    it('should throw on error for getRAList', async () => {
      mockAxiosClient.get.mockRejectedValue(new Error('fail'));
      await expect(require('../../src/services/services').getRAList({})).rejects.toThrow('fail');
    });
    it('should throw on error for getRAStringOptions', async () => {
      mockAxiosClient.get.mockRejectedValue(new Error('fail'));
      await expect(require('../../src/services/services').getRAStringOptions('vessel_category')).rejects.toThrow('fail');
    });
    it('should throw on error for getTemplateById', async () => {
      mockAxiosClient.get.mockRejectedValue(new Error('fail'));
      await expect(require('../../src/services/services').getTemplateById('id')).rejects.toThrow('fail');
    });
    it('should throw on error for deleteTemplateById', async () => {
      mockAxiosClient.delete.mockRejectedValue(new Error('fail'));
      await expect(require('../../src/services/services').deleteTemplateById(1)).rejects.toThrow('fail');
    });
    it('should throw on error for deleteRiskById', async () => {
      mockAxiosClient.delete.mockRejectedValue(new Error('fail'));
      await expect(require('../../src/services/services').deleteRiskById(1)).rejects.toThrow('fail');
    });
    it('should throw on error for createNewTemplate', async () => {
      mockAxiosClient.post.mockRejectedValue(new Error('fail'));
      await expect(require('../../src/services/services').createNewTemplate({})).rejects.toThrow('fail');
    });
    it('should throw on error for updateSavedTemplate', async () => {
      mockAxiosClient.patch.mockRejectedValue(new Error('fail'));
      await expect(require('../../src/services/services').updateSavedTemplate(1, {})).rejects.toThrow('fail');
    });
    it('should throw on error for getVesselsList', async () => {
      mockAxiosClient.get.mockRejectedValue(new Error('fail'));
      await expect(require('../../src/services/services').getVesselsList()).rejects.toThrow('fail');
    });
    it('should throw on error for getOfficesList', async () => {
      mockAxiosClient.get.mockRejectedValue(new Error('fail'));
      await expect(require('../../src/services/services').getOfficesList()).rejects.toThrow('fail');
    });

    // Tests for remaining uncovered functions
    it('getRiskById should GET and return data', async () => {
      const mockData = { id: 'risk123', name: 'Test Risk' };
      mockAxiosClient.get.mockResolvedValue({ data: mockData });
      const result = await require('../../src/services/services').getRiskById('risk123');
      expect(mockAxiosClient.get).toHaveBeenCalledWith('https://api.test.com/risks/risk123');
      expect(result).toEqual(mockData);
    });

    it('should throw on error for getRiskById', async () => {
      mockAxiosClient.get.mockRejectedValue(new Error('Risk fetch error'));
      await expect(require('../../src/services/services').getRiskById('123')).rejects.toThrow('Risk fetch error');
    });

    it('getCrewList should GET with vessel_id param and return crewList', async () => {
      const mockCrewList = [
        { id: 1, name: 'John Doe', rank: 'Captain' },
        { id: 2, name: 'Jane Smith', rank: 'Engineer' }
      ];
      mockAxiosClient.get.mockResolvedValue({ data: { crewList: mockCrewList } });
      const result = await require('../../src/services/services').getCrewList(456);
      expect(mockAxiosClient.get).toHaveBeenCalledWith('https://api.test.com/crew-list', {
        params: { vessel_id: 456 }
      });
      expect(result).toEqual(mockCrewList);
    });

    it('should throw on error for getCrewList', async () => {
      mockAxiosClient.get.mockRejectedValue(new Error('Crew fetch error'));
      await expect(require('../../src/services/services').getCrewList(123)).rejects.toThrow('Crew fetch error');
    });

    it('createNewRA should POST and return data', async () => {
      const mockData = { id: 1, status: 'created' };
      const payload = { title: 'New Risk Assessment' };
      mockAxiosClient.post.mockResolvedValue({ data: mockData });
      const result = await require('../../src/services/services').createNewRA(payload);
      expect(mockAxiosClient.post).toHaveBeenCalledWith('https://api.test.com/risks', payload);
      expect(result).toEqual(mockData);
    });

    it('should throw on error for createNewRA', async () => {
      mockAxiosClient.post.mockRejectedValue(new Error('RA creation error'));
      await expect(require('../../src/services/services').createNewRA({})).rejects.toThrow('RA creation error');
    });

    it('updateSavedRA should PATCH and return data', async () => {
      const mockData = { id: 789, status: 'updated' };
      const payload = { title: 'Updated Risk Assessment' };
      mockAxiosClient.patch.mockResolvedValue({ data: mockData });
      const result = await require('../../src/services/services').updateSavedRA(789, payload);
      expect(mockAxiosClient.patch).toHaveBeenCalledWith('https://api.test.com/risks/789', payload);
      expect(result).toEqual(mockData);
    });

    it('should throw on error for updateSavedRA', async () => {
      mockAxiosClient.patch.mockRejectedValue(new Error('RA update error'));
      await expect(require('../../src/services/services').updateSavedRA(1, {})).rejects.toThrow('RA update error');
    });

    it('getApprovalsRequiredList should GET and return data', async () => {
      const mockData = [
        { id: 1, name: 'Manager Approval' },
        { id: 2, name: 'Safety Officer Approval' }
      ];
      mockAxiosClient.get.mockResolvedValue({ data: mockData });
      const assessor = 1;
      const result = await require('../../src/services/services').getApprovalsRequiredList(assessor);
      expect(mockAxiosClient.get).toHaveBeenCalledWith('https://api.test.com/approval-required/1');
      expect(result).toEqual(mockData);
    });

    it('should throw on error for getApprovalsRequiredList', async () => {
      mockAxiosClient.get.mockRejectedValue(new Error('Approvals fetch error'));
      await expect(require('../../src/services/services').getApprovalsRequiredList()).rejects.toThrow('Approvals fetch error');
    });

    it('getSeafarerRanks should GET with ranks query param and return data', async () => {
      const mockData = [
        { id: 1, rank: 'Captain' },
        { id: 2, rank: 'Chief Engineer' },
        { id: 3, rank: 'First Officer' }
      ];
      mockAxiosClient.get.mockResolvedValue({ data: mockData });
      const result = await require('../../src/services/services').getSeafarerRanks();
      expect(mockAxiosClient.get).toHaveBeenCalledWith('https://api.test.com/seafarer-lookup?values=ranks');
      expect(result).toEqual(mockData);
    });

    it('should throw on error for getSeafarerRanks', async () => {
      mockAxiosClient.get.mockRejectedValue(new Error('Seafarer ranks fetch error'));
      await expect(require('../../src/services/services').getSeafarerRanks()).rejects.toThrow('Seafarer ranks fetch error');
    });
  });
});
