// Store interceptor handlers for testing
let requestInterceptorHandlers: any[] = [];
let responseInterceptorHandlers: any[] = [];

const mockAxiosInstance = {
  get: jest.fn(() =>
    Promise.resolve({
      data: {},
      status: 300,
    }),
  ),
  post: jest.fn(),
  delete: jest.fn(),
  put: jest.fn(),
  patch: jest.fn(),
  interceptors: {
    request: {
      use: jest.fn((successHandler: any, errorHandler: any) => {
        requestInterceptorHandlers.push({successHandler, errorHandler});
        return requestInterceptorHandlers.length - 1;
      }),
    },
    response: {
      use: jest.fn((successHandler: any, errorHandler: any) => {
        responseInterceptorHandlers.push({successHandler, errorHandler});
        return responseInterceptorHandlers.length - 1;
      }),
    },
  },
  getUri: jest.fn(({url}: {url: string}) => url),
};

module.exports = {
  ...mockAxiosInstance,
  create: jest.fn(() => mockAxiosInstance),
  isCancel: jest.fn(),
  AxiosHeaders: jest.fn().mockImplementation(headers => {
    const headersMap = new Map();
    Object.entries(headers || {}).forEach(([key, value]) => {
      headersMap.set(key, value);
    });
    return {
      get: (key: string) => headersMap.get(key),
      set: (key: string, value: string) => headersMap.set(key, value),
      has: (key: string) => headersMap.has(key),
      delete: (key: string) => headersMap.delete(key),
      ...headers,
    };
  }),
  // Export handlers for testing
  __getRequestInterceptorHandlers: () => requestInterceptorHandlers,
  __getResponseInterceptorHandlers: () => responseInterceptorHandlers,
  __clearInterceptorHandlers: () => {
    requestInterceptorHandlers = [];
    responseInterceptorHandlers = [];
  },
};
