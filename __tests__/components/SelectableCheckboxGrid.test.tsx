import React from 'react';
import {render, screen, fireEvent} from '@testing-library/react';
import SelectableCheckboxGrid from '../../src/components/SelectableCheckboxGrid';
import {RiskCategory} from '../../src/context/DataStoreProvider';

const mockOptions: RiskCategory[] = [
  {id: 1, name: 'Hazard A'},
  {id: 2, name: '<PERSON>zard B'},
  {id: 3, name: 'Hazard C'},
];

describe('SelectableCheckboxGrid', () => {
  const mockOnChange = jest.fn();
  const mockOnOthersChange = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders title, subtitle, and checkboxes', () => {
    render(
      <SelectableCheckboxGrid
        title="Risk Title"
        subtitle="Select Hazards"
        searchPlaceholder="Search Hazards"
        options={mockOptions}
        onChange={mockOnChange}
      />,
    );

    expect(screen.getByText('Risk Title')).toBeInTheDocument();
    expect(screen.getByText('Select Hazards')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('Search Hazards')).toBeInTheDocument();

    mockOptions.forEach(opt => {
      expect(screen.getByLabelText(opt.name)).toBeInTheDocument();
    });
  });

  it('calls onChange when a checkbox is selected or unselected', () => {
    render(
      <SelectableCheckboxGrid
        title="Risk Title"
        subtitle="Select Hazards"
        searchPlaceholder="Search Hazards"
        options={mockOptions}
        onChange={mockOnChange}
      />,
    );

    const checkbox = screen.getByLabelText('Hazard A');
    fireEvent.click(checkbox);

    expect(mockOnChange).toHaveBeenCalledWith([1]);

    // Uncheck it
    fireEvent.click(screen.getByLabelText('Hazard A'));
    expect(mockOnChange).toHaveBeenCalledWith([]);
  });

  it('filters unchecked options based on search input', () => {
    render(
      <SelectableCheckboxGrid
        title="Risk Title"
        subtitle="Select Hazards"
        searchPlaceholder="Search Hazards"
        options={mockOptions}
        onChange={mockOnChange}
      />,
    );

    const input = screen.getByPlaceholderText('Search Hazards');
    fireEvent.change(input, {target: {value: 'B'}});

    expect(screen.queryByLabelText('Hazard A')).not.toBeInTheDocument();
    expect(screen.getByLabelText('Hazard B')).toBeInTheDocument();
    expect(screen.queryByLabelText('Hazard C')).not.toBeInTheDocument();
  });

  it('renders and handles "Others" checkbox and input when selected', () => {
    render(
      <SelectableCheckboxGrid
        title="Risk Title"
        subtitle="Select Hazards"
        searchPlaceholder="Search Hazards"
        options={mockOptions}
        onChange={mockOnChange}
        hasOthers={true}
        isOthersSelected={true}
        othersText="Initial"
        onOthersChange={mockOnOthersChange}
      />,
    );

    const input = screen.getByPlaceholderText('Please specify');
    expect(input).toBeInTheDocument();
    expect(input).toHaveValue('Initial');

    fireEvent.change(input, {target: {value: 'Other hazard'}});
    expect(mockOnOthersChange).toHaveBeenCalledWith(true, 'Other hazard');

    const othersCheckbox = screen.getByLabelText('Others');
    fireEvent.click(othersCheckbox);
    expect(mockOnOthersChange).toHaveBeenCalledWith(false, '');
  });

  it('renders "Others" checkbox when not selected', () => {
    render(
      <SelectableCheckboxGrid
        title="Risk Title"
        subtitle="Select Hazards"
        searchPlaceholder="Search Hazards"
        options={mockOptions}
        onChange={mockOnChange}
        hasOthers={true}
        isOthersSelected={false}
        onOthersChange={mockOnOthersChange}
      />,
    );

    const othersCheckbox = screen.getByLabelText('Others');
    expect(othersCheckbox).toBeInTheDocument();
    expect(
      screen.queryByPlaceholderText('Please specify'),
    ).not.toBeInTheDocument();

    fireEvent.click(othersCheckbox);
    expect(mockOnOthersChange).toHaveBeenCalledWith(true, '');
  });
});
