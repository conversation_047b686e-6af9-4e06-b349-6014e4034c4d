import React from 'react';
import {render} from '@testing-library/react';
import TrashIcon from '../../../src/components/icons/trash-icon';

describe('TrashIcon', () => {
  it('renders with default props', () => {
    const {container} = render(<TrashIcon />);
    const svg = container.querySelector('svg');
    expect(svg).toBeInTheDocument();
    expect(svg).toHaveAttribute('width', '12');
    expect(svg).toHaveAttribute('height', '14');
    expect(container.querySelector('path')).toHaveAttribute('fill', '#1F4A70');
  });

  it('renders with custom width, height, and color', () => {
    const {container} = render(
      <TrashIcon
        width={20}
        height={22}
        color="#ff0000"
        className="custom-class"
        data-testid="icon"
      />,
    );
    const svg = container.querySelector('svg');
    expect(svg).toHaveAttribute('width', '20');
    expect(svg).toHaveAttribute('height', '22');
    expect(container.querySelector('path')).toHaveAttribute('fill', '#ff0000');
    expect(svg).toHaveClass('custom-class');
  });
});
