import React from 'react';
import {render} from '@testing-library/react';
import '@testing-library/jest-dom';
import PlusIcon from '../../../src/components/icons/plus-icon';

describe('PlusIcon', () => {
  it('renders with default size and color', () => {
    const {container} = render(<PlusIcon />);
    const svg = container.querySelector('svg');
    expect(svg).toHaveAttribute('width', '20');
    expect(svg).toHaveAttribute('height', '20');
    expect(svg).toHaveAttribute('viewBox', '0 0 20 20');
    const path = container.querySelector('path');
    expect(path).toHaveAttribute('fill', 'currentColor');
  });

  it('applies custom width and height', () => {
    const {container} = render(<PlusIcon width={32} height={32} />);
    const svg = container.querySelector('svg');
    expect(svg).toHaveAttribute('width', '32');
    expect(svg).toHaveAttribute('height', '32');
  });

  it('applies custom color', () => {
    const {container} = render(<PlusIcon color="#ff0000" />);
    const path = container.querySelector('path');
    expect(path).toHaveAttribute('fill', '#ff0000');
  });

  it('applies custom className and style', () => {
    const {container} = render(
      <PlusIcon className="test-class" style={{marginLeft: '10px'}} />,
    );
    const svg = container.querySelector('svg');
    expect(svg).toHaveClass('test-class');
    expect(svg).toHaveStyle('margin-left: 10px');
  });
});
