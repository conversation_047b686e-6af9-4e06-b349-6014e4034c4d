import React from 'react';
import {render} from '@testing-library/react';
import ExternalLinkIcon from '../../../src/components/icons/external-link-icon';

describe('ExternalLinkIcon', () => {
  it('renders with default props', () => {
    const {container} = render(<ExternalLinkIcon />);
    const svg = container.querySelector('svg');
    expect(svg).toBeInTheDocument();
    expect(svg).toHaveAttribute('width', '16');
    expect(svg).toHaveAttribute('height', '16');
    // Default color
    expect(container.querySelector('path')).toHaveAttribute('fill', '#1F4A70');
  });

  it('renders with custom size and color', () => {
    const {container} = render(
      <ExternalLinkIcon size={24} color="#ff0000" data-testid="icon" />,
    );
    const svg = container.querySelector('svg');
    expect(svg).toHaveAttribute('width', '24');
    expect(svg).toHaveAttribute('height', '24');
    expect(container.querySelector('path')).toHaveAttribute('fill', '#ff0000');
  });

  it('forwards extra props', () => {
    const {container} = render(<ExternalLinkIcon data-testid="icon" />);
    expect(container.querySelector('svg')).toHaveAttribute(
      'data-testid',
      'icon',
    );
  });
});
