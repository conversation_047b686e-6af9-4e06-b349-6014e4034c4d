import {render} from '@testing-library/react';
import CalendarIcon from '../../../src/components/icons/calendar-icon';

describe('CalendarIcon', () => {
  it('renders with default props', () => {
    const {container} = render(<CalendarIcon />);
    const svg = container.querySelector('svg');
    expect(svg).toBeInTheDocument();
    expect(svg).toHaveAttribute('width', '15');
    expect(svg).toHaveAttribute('height', '14');
    expect(svg).toHaveAttribute('viewBox', '0 0 15 14');
    expect(svg).toHaveAttribute('fill', 'none');
  });

  it('renders with custom size and color', () => {
    const {container} = render(
      <CalendarIcon width={24} height={24} data-testid="icon" />,
    );
    const svg = container.querySelector('svg');
    expect(svg).toHaveAttribute('width', '24');
    expect(svg).toHaveAttribute('height', '24');
    expect(svg).toHaveAttribute('data-testid', 'icon');
  });

  it('forwards extra props', () => {
    const {container} = render(<CalendarIcon data-testid="icon" />);
    expect(container.querySelector('svg')).toHaveAttribute(
      'data-testid',
      'icon',
    );
  });

  it('has correct path element with default styling', () => {
    const {container} = render(<CalendarIcon />);
    const path = container.querySelector('path');
    expect(path).toBeInTheDocument();
    expect(path).toHaveAttribute('fill', '#6C757D');
    expect(path).toHaveAttribute('stroke', '#6C757D');
    expect(path).toHaveAttribute('stroke-width', '0.2');
  });
});
