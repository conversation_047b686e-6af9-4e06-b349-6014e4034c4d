import React from 'react';
import {render, screen, fireEvent, waitFor} from '@testing-library/react';
import GenericStepper, {StepConfig} from '../../src/components/GenericStepper';

describe('GenericStepper', () => {
  const steps: StepConfig[] = [
    {
      label: 'Step 1',
      component: <div data-testid="step-1">Step 1 Content</div>,
    },
    {
      label: 'Step 2',
      component: <div data-testid="step-2">Step 2 Content</div>,
    },
  ];

  const mockPrimaryClick = jest.fn();
  const mockSecondaryClick = jest.fn();
  const mockOnNext = jest.fn();
  const mockOnClose = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders first step content and breadcrumb title', () => {
    render(
      <GenericStepper
        steps={steps}
        breadCrumbTitle="Project Wizard"
        primaryBtnTitle="Next"
        primaryBtnOnClick={mockPrimaryClick}
      />,
    );

    expect(screen.getByText('Project Wizard')).toBeInTheDocument();
    expect(screen.getByTestId('step-1')).toBeInTheDocument();
    expect(screen.queryByTestId('step-2')).not.toBeInTheDocument();
  });

  it('calls onClose when close icon is clicked', () => {
    render(
      <GenericStepper
        steps={steps}
        breadCrumbTitle="Project Wizard"
        primaryBtnTitle="Next"
        onClose={mockOnClose}
        primaryBtnOnClick={mockPrimaryClick}
      />,
    );

    const closeBtn = screen.getByRole('button', {name: /close/i});
    fireEvent.click(closeBtn);
    expect(mockOnClose).toHaveBeenCalled();
  });

  it('navigates to next step on Next click and calls onNext', async () => {
    mockOnNext.mockResolvedValue(undefined);

    render(
      <GenericStepper
        steps={steps}
        primaryBtnTitle="Next"
        onNext={mockOnNext}
        primaryBtnOnClick={mockPrimaryClick}
      />,
    );

    const nextBtn = screen.getByTestId('form-prj-save-btn');
    fireEvent.click(nextBtn);

    await waitFor(() => {
      expect(mockOnNext).toHaveBeenCalledWith(1);
      expect(screen.getByTestId('step-2')).toBeInTheDocument();
    });
  });

  it('calls primaryBtnOnClick on final step', async () => {
    mockPrimaryClick.mockResolvedValue(undefined);

    render(
      <GenericStepper
        steps={steps}
        primaryBtnTitle="Finish"
        primaryBtnOnClick={mockPrimaryClick}
      />,
    );

    // Step 1 -> Step 2
    fireEvent.click(screen.getByTestId('form-prj-save-btn'));

    // Step 2 -> Finish (triggers primaryBtnOnClick)
    await waitFor(() =>
      fireEvent.click(screen.getByTestId('form-prj-save-btn')),
    );

    expect(mockPrimaryClick).toHaveBeenCalled();
  });

  it('calls secondaryBtnOnClick when Cancel is clicked', () => {
    render(
      <GenericStepper
        steps={steps}
        primaryBtnTitle="Next"
        secondaryBtnTitle="Cancel"
        primaryBtnOnClick={mockPrimaryClick}
        secondaryBtnOnClick={mockSecondaryClick}
      />,
    );

    fireEvent.click(screen.getByTestId('form-prj-cancel-btn'));
    expect(mockSecondaryClick).toHaveBeenCalled();
  });

  it('disables buttons when disabled props are passed', () => {
    render(
      <GenericStepper
        steps={steps}
        primaryBtnTitle="Next"
        primaryBtnDisabled
        secondaryBtnDisabled
        primaryBtnOnClick={mockPrimaryClick}
      />,
    );

    expect(screen.getByTestId('form-prj-save-btn')).toBeDisabled();
    expect(screen.getByTestId('form-prj-cancel-btn')).toBeDisabled();
  });

  it('supports function-based primary button title', () => {
    const titleFn = (currentStep: number, totalSteps: number) =>
      currentStep === totalSteps ? 'Submit' : 'Continue';

    render(
      <GenericStepper
        steps={steps}
        primaryBtnTitle={titleFn}
        primaryBtnOnClick={mockPrimaryClick}
      />,
    );

    expect(screen.getByTestId('form-prj-save-btn')).toHaveTextContent(
      'Continue',
    );

    fireEvent.click(screen.getByTestId('form-prj-save-btn'));

    expect(screen.getByTestId('form-prj-save-btn')).toHaveTextContent('Submit');
  });
});
