import React from 'react';
import {render, screen, fireEvent} from '@testing-library/react';
import '@testing-library/jest-dom';
import Stepper from '../../src/components/Stepper';

describe('Stepper Component', () => {
  const mockSteps = ['First Step', 'Second Step', 'Third Step'];
  const mockCurrentStep = 2;
  const mockSetStep = jest.fn();

  beforeEach(() => {
    mockSetStep.mockClear();
  });

  it('renders the correct number of steps', () => {
    render(
      <Stepper
        steps={mockSteps}
        currentStep={mockCurrentStep}
        setStep={mockSetStep}
      />,
    );
    const stepNumbers = screen.getAllByText(/^Step [1-3]$/);
    expect(stepNumbers).toHaveLength(mockSteps.length);
  });

  it('displays the correct labels for each step', () => {
    render(
      <Stepper
        steps={mockSteps}
        currentStep={mockCurrentStep}
        setStep={mockSetStep}
      />,
    );
    mockSteps.forEach(label => {
      expect(screen.getByText(label)).toBeInTheDocument();
    });
  });

  it('highlights the current active step', () => {
    render(
      <Stepper
        steps={mockSteps}
        currentStep={mockCurrentStep}
        setStep={mockSetStep}
      />,
    );
    const activeStepCard = screen.getByText('Second Step').closest('.card');
    expect(activeStepCard).toHaveStyle('border: 2px solid #A6CBF3');
    expect(activeStepCard).toHaveStyle(
      'box-shadow: 0 0 0 1px rgba(59, 130, 246, 0.2)',
    );
  });

  it('shows checkmark for completed steps', () => {
    render(
      <Stepper
        steps={mockSteps}
        currentStep={mockCurrentStep}
        setStep={mockSetStep}
      />,
    );
    const completedStepIcon = screen
      .getByText('First Step')
      .closest('.card')
      ?.querySelector('svg');
    expect(completedStepIcon).toBeInTheDocument();
  });

  it('shows empty circle for incomplete steps', () => {
    render(
      <Stepper
        steps={mockSteps}
        currentStep={mockCurrentStep}
        setStep={mockSetStep}
      />,
    );
    const incompleteStepIcon = screen
      .getByText('Third Step')
      .closest('.card')
      ?.querySelector('div[style*="border: 1px solid #ccc"]');
    expect(incompleteStepIcon).toBeInTheDocument();
  });

  it('allows clicking on completed steps', () => {
    render(
      <Stepper
        steps={mockSteps}
        currentStep={mockCurrentStep}
        setStep={mockSetStep}
      />,
    );
    const completedStep = screen.getByText('First Step').closest('.card');
    fireEvent.click(completedStep!);
    expect(mockSetStep).toHaveBeenCalledWith(1);
  });

  it('does not allow clicking on current or future steps', () => {
    render(
      <Stepper
        steps={mockSteps}
        currentStep={mockCurrentStep}
        setStep={mockSetStep}
      />,
    );

    // Current step
    const currentStep = screen.getByText('Second Step').closest('.card');
    fireEvent.click(currentStep!);
    expect(mockSetStep).not.toHaveBeenCalled();

    // Future step
    const futureStep = screen.getByText('Third Step').closest('.card');
    fireEvent.click(futureStep!);
    expect(mockSetStep).not.toHaveBeenCalled();
  });

  it('applies correct cursor style based on step state', () => {
    render(
      <Stepper
        steps={mockSteps}
        currentStep={mockCurrentStep}
        setStep={mockSetStep}
      />,
    );

    const completedStep = screen.getByText('First Step').closest('.card');
    expect(completedStep).toHaveStyle('cursor: pointer');

    const currentStep = screen.getByText('Second Step').closest('.card');
    expect(currentStep).toHaveStyle('cursor: default');

    const futureStep = screen.getByText('Third Step').closest('.card');
    expect(futureStep).toHaveStyle('cursor: default');
  });
});
