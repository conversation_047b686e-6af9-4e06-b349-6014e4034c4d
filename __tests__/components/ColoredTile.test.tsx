import React from 'react';
import { render } from '@testing-library/react';
import ColoredTile, { ColoredTileTheme } from '../../src/components/ColoredTile';

describe('ColoredTile', () => {
  const themes: ColoredTileTheme[] = ['red', 'green', 'yellow', 'blue'];

  it('renders the text prop', () => {
    const { getByText } = render(
      <ColoredTile text="Test Tile" theme="red" />
    );
    expect(getByText('Test Tile')).toBeInTheDocument();
  });

  it('applies the correct theme class', () => {
    themes.forEach(theme => {
      const { container, unmount } = render(
        <ColoredTile text="Theme Tile" theme={theme} />
      );
      expect(container.firstChild).toHaveClass(`colored-tile-${theme}`);
      unmount();
    });
  });

  it('applies additional className if provided', () => {
    const { container } = render(
      <ColoredTile text="Class Tile" theme="green" className="extra-class" />
    );
    expect(container.firstChild).toHaveClass('extra-class');
  });

  it('renders as a <span> element', () => {
    const { container } = render(
      <ColoredTile text="Span Tile" theme="yellow" />
    );
    expect(container.firstChild?.nodeName).toBe('SPAN');
  });
});
