import React from 'react';
import {render, screen, fireEvent, waitFor} from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import EditBasicDetailsComp from '../../src/components/EditBasicDetail';
import {TemplateForm} from '../../src/types/template';
import {RiskForm} from '../../src/types/risk';
import {
  getApprovalsRequiredList,
  getOfficesList,
  getVesselsList,
} from '../../src/services/services';

// Mock the services
jest.mock('../../src/services/services');
jest.mock('../../src/utils/helper', () => ({
  formatDateToYYYYMMDD: jest.fn((date: Date) => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  }),
}));

// Mock the assessorOptions
jest.mock('../../src/pages/CreateRA/BasicDetails', () => ({
  assessorOptions: [
    {value: 1, label: 'Office'},
    {value: 2, label: 'Vessel'},
  ],
}));

// Mock DropdownTypeahead component
jest.mock('../../src/components/DropdownTypeahead', () => {
  return function MockDropdownTypeahead({
    label,
    options,
    selected,
    onChange,
    multiple,
    isInvalid,
    errorMessage,
  }: any) {
    const handleChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
      const value = e.target.value;
      if (multiple) {
        // For multiple selection, simulate selecting multiple options
        const mockSelectedOptions = value
          ? [
              {
                value: parseInt(value),
                label:
                  options.find((opt: any) => opt.value.toString() === value)
                    ?.label || '',
              },
            ]
          : [];
        onChange(mockSelectedOptions);
      } else {
        const selectedOption = options.find(
          (opt: any) => opt.value.toString() === value,
        );
        onChange(selectedOption || null);
      }
    };

    const getSelectedValue = () => {
      if (multiple) {
        return Array.isArray(selected)
          ? selected.map(s => s.value.toString())
          : [];
      }
      return selected?.value?.toString() || '';
    };

    return (
      <div data-testid={`dropdown-${label.toLowerCase().replace(/\s+/g, '-')}`}>
        <label>{label}</label>
        <select
          data-testid={`${label.toLowerCase().replace(/\s+/g, '-')}-select`}
          onChange={handleChange}
          multiple={multiple}
          value={getSelectedValue()}
        >
          <option value="">Select {label}</option>
          {options.map((option: any) => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>
        {isInvalid && (
          <div
            className="invalid-feedback"
            data-testid={`${label.toLowerCase().replace(/\s+/g, '-')}-error`}
          >
            {errorMessage}
          </div>
        )}
      </div>
    );
  };
});

// Mock CustomDatePicker component
jest.mock('../../src/components/CustomDatePicker', () => {
  return function MockCustomDatePicker({
    label,
    value,
    onChange,
    isRequired,
    errorMsg,
  }: any) {
    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const dateValue = e.target.value ? new Date(e.target.value) : undefined;
      onChange(dateValue);
    };

    const formatDateForInput = (date: Date) => {
      return date.toISOString().split('T')[0];
    };

    return (
      <div
        data-testid={`datepicker-${label.toLowerCase().replace(/\s+/g, '-')}`}
      >
        <label>{label}</label>
        <input
          type="date"
          data-testid={`${label.toLowerCase().replace(/\s+/g, '-')}-input`}
          value={value ? formatDateForInput(value) : ''}
          onChange={handleChange}
        />
        {isRequired && !value && errorMsg && (
          <div
            className="invalid-feedback"
            data-testid={`${label.toLowerCase().replace(/\s+/g, '-')}-error`}
          >
            {errorMsg}
          </div>
        )}
      </div>
    );
  };
});

const mockGetVesselsList = getVesselsList as jest.MockedFunction<
  typeof getVesselsList
>;
const mockGetOfficesList = getOfficesList as jest.MockedFunction<
  typeof getOfficesList
>;
const mockGetApprovalsRequiredList =
  getApprovalsRequiredList as jest.MockedFunction<
    typeof getApprovalsRequiredList
  >;

// Mock useDataStoreContext
jest.mock('../../src/context', () => ({
  useDataStoreContext: () => ({
    roleConfig: {user: {id: 1, name: 'Test User', email: '<EMAIL>'}},
  }),
}));

describe('EditBasicDetailsComp', () => {
  const mockSetClonedForm = jest.fn();

  const mockTemplateForm: TemplateForm = {
    task_requiring_ra: 'Test task',
    task_duration: '5 days',
    task_alternative_consideration: 'Test consideration',
    task_rejection_reason: 'Test reason',
    worst_case_scenario: 'Test scenario',
    recovery_measures: 'Test measures',
    status: 'DRAFT' as any,
    template_category: {category_id: []},
    template_hazard: {is_other: false, value: '', hazard_id: []},
    parameters: [],
    template_job: [],
    template_task_reliability_assessment: [],
    template_keyword: [],
  };

  const mockRiskForm: RiskForm = {
    ...mockTemplateForm,
    assessor: 1,
    vessel_ownership_id: 1,
    date_risk_assessment: '2024-01-15',
    approval_required: [1, 2],
    approval_date: '2024-01-20',
    risk_approver: [
      {
        id: 1,
        risk_id: 1,
        keycloak_id: 'abc-123',
        user_name: 'Approver One',
        user_email: '<EMAIL>',
        job_title: 'Manager',
        message: null,
        approval_order: 1,
        approval_status: 0,
        approval_date: null,
        status: 1,
      },
      {
        id: 2,
        risk_id: 1,
        keycloak_id: 'def-456',
        user_name: 'Approver Two',
        user_email: '<EMAIL>',
        job_title: 'Supervisor',
        message: null,
        approval_order: 2,
        approval_status: null,
        approval_date: null,
        status: 0,
      },
    ],
    risk_team_member: [],
    risk_category: {is_other: false, category_id: [], value: ''},
    risk_hazard: {is_other: false, hazard_id: [], value: ''},
    risk_job: [],
    risk_task_reliability_assessment: [],
    ra_level: undefined,
  };

  const mockVesselsData = [
    {
      id: 1,
      name: 'Vessel 1',
      vessel: {id: 101},
      vessel_account_code_new: 'VAC1',
      images: [],
      sma_entity: {
        id: 1,
        type: 'TypeA',
        name: 'SMA Entity 1',
        ship_party_id: 1001,
      },
      status: 'Active',
      fleet_staff: {
        qhse_deputy_general_manager: {
          id: '1',
          full_name: 'Manager Name',
          email: '<EMAIL>',
        },
      },
    },
    {
      id: 2,
      name: 'Vessel 2',
      vessel: {id: 102},
      vessel_account_code_new: 'VAC2',
      images: [],
      sma_entity: {
        id: 2,
        type: 'TypeB',
        name: 'SMA Entity 2',
        ship_party_id: 1002,
      },
      status: 'Active',
      fleet_staff: {
        qhse_deputy_general_manager: {
          id: '2',
          full_name: 'Manager Two',
          email: '<EMAIL>',
        },
      },
    },
  ];

  const mockOfficesData = [
    {id: 1, value: 'Office 1', ship_party_id: 201},
    {id: 2, value: 'Office 2', ship_party_id: 202},
  ];

  const mockApprovalsData = [
    {id: 1, name: 'Approval 1'},
    {id: 2, name: 'Approval 2'},
    {id: 3, name: 'Approval 3'},
  ];

  beforeEach(() => {
    jest.clearAllMocks();
    mockGetVesselsList.mockResolvedValue(mockVesselsData);
    mockGetOfficesList.mockResolvedValue(mockOfficesData);
    mockGetApprovalsRequiredList.mockResolvedValue(mockApprovalsData);
  });

  describe('Template Form Tests', () => {
    it('renders template form fields correctly', () => {
      render(
        <EditBasicDetailsComp
          clonedForm={mockTemplateForm}
          setClonedForm={mockSetClonedForm}
          type="template"
        />,
      );

      expect(screen.getByLabelText('Task Requiring R.A.')).toBeInTheDocument();
      expect(screen.getByLabelText('Task Duration')).toBeInTheDocument();
      expect(screen.getByDisplayValue('Test task')).toBeInTheDocument();
      expect(screen.getByDisplayValue('5 days')).toBeInTheDocument();
    });

    it('updates task requiring RA field', async () => {
      const user = userEvent.setup();
      render(
        <EditBasicDetailsComp
          clonedForm={mockTemplateForm}
          setClonedForm={mockSetClonedForm}
          type="template"
        />,
      );

      const taskInput = screen.getByLabelText('Task Requiring R.A.');
      await user.type(taskInput, 'X');

      // Check that setClonedForm was called (the exact value may vary due to typing behavior)
      expect(mockSetClonedForm).toHaveBeenCalled();
      const lastCall =
        mockSetClonedForm.mock.calls[
          mockSetClonedForm.mock.calls.length - 1
        ][0];
      expect(lastCall.task_requiring_ra).toContain('X');
    });

    it('updates task duration field', async () => {
      const user = userEvent.setup();
      render(
        <EditBasicDetailsComp
          clonedForm={mockTemplateForm}
          setClonedForm={mockSetClonedForm}
          type="template"
        />,
      );

      const durationInput = screen.getByLabelText('Task Duration');
      await user.type(durationInput, 'X');

      // Check that setClonedForm was called (the exact value may vary due to typing behavior)
      expect(mockSetClonedForm).toHaveBeenCalled();
      const lastCall =
        mockSetClonedForm.mock.calls[
          mockSetClonedForm.mock.calls.length - 1
        ][0];
      expect(lastCall.task_duration).toContain('X');
    });

    it('shows validation errors for empty required fields', () => {
      const emptyForm = {
        ...mockTemplateForm,
        task_requiring_ra: '',
        task_duration: '',
      };
      render(
        <EditBasicDetailsComp
          clonedForm={emptyForm}
          setClonedForm={mockSetClonedForm}
          type="template"
        />,
      );

      const taskInput = screen.getByLabelText('Task Requiring R.A.');
      const durationInput = screen.getByLabelText('Task Duration');

      expect(taskInput).toHaveClass('is-invalid');
      expect(durationInput).toHaveClass('is-invalid');
    });

    it('does not render risk-specific fields for template type', () => {
      render(
        <EditBasicDetailsComp
          clonedForm={mockTemplateForm}
          setClonedForm={mockSetClonedForm}
          type="template"
        />,
      );

      expect(screen.queryByText('Assessor')).not.toBeInTheDocument();
      expect(
        screen.queryByText('Date of Risk Assessment'),
      ).not.toBeInTheDocument();
    });
  });

  describe('Risk Form Tests', () => {
    it('renders risk form fields correctly', async () => {
      render(
        <EditBasicDetailsComp
          clonedForm={mockRiskForm}
          setClonedForm={mockSetClonedForm}
          type="risk"
        />,
      );

      await waitFor(() => {
        expect(screen.getByText('Assessor')).toBeInTheDocument();
        expect(screen.getByText('Date of Risk Assessment')).toBeInTheDocument();
        expect(
          screen.getByText('Approvals Required (if necessary)'),
        ).toBeInTheDocument();
      });
    });

    it('loads vessel, office, and approval options on mount for risk type', async () => {
      render(
        <EditBasicDetailsComp
          clonedForm={mockRiskForm}
          setClonedForm={mockSetClonedForm}
          type="risk"
        />,
      );

      await waitFor(() => {
        expect(mockGetVesselsList).toHaveBeenCalled();
        expect(mockGetOfficesList).toHaveBeenCalled();
        expect(mockGetApprovalsRequiredList).toHaveBeenCalled();
      });
    });

    it('handles assessor dropdown change', async () => {
      render(
        <EditBasicDetailsComp
          clonedForm={mockRiskForm}
          setClonedForm={mockSetClonedForm}
          type="risk"
        />,
      );

      await waitFor(() => {
        const assessorSelect = screen.getByTestId('assessor-select');
        fireEvent.change(assessorSelect, {target: {value: '2'}});
      });

      expect(mockSetClonedForm).toHaveBeenCalledWith({
        ...mockRiskForm,
        assessor: 2,
      });
    });

    it('handles vessel/office dropdown change for vessel assessor', async () => {
      const vesselAssessorForm = {...mockRiskForm, assessor: 2};
      render(
        <EditBasicDetailsComp
          clonedForm={vesselAssessorForm}
          setClonedForm={mockSetClonedForm}
          type="risk"
        />,
      );

      await waitFor(() => {
        const vesselOfficeSelect = screen.getByTestId('vessel/office-select');
        fireEvent.change(vesselOfficeSelect, {target: {value: '1'}});
      });

      // Check that the function was called (vessel logic is tested in the component)
      expect(mockSetClonedForm).toHaveBeenCalled();
    });

    it('handles vessel/office dropdown change for office assessor', async () => {
      const officeAssessorForm = {...mockRiskForm, assessor: 1};
      render(
        <EditBasicDetailsComp
          clonedForm={officeAssessorForm}
          setClonedForm={mockSetClonedForm}
          type="risk"
        />,
      );

      await waitFor(() => {
        const vesselOfficeSelect = screen.getByTestId('vessel/office-select');
        fireEvent.change(vesselOfficeSelect, {target: {value: '1'}});
      });

      // Check that the function was called (office logic is tested in the component)
      expect(mockSetClonedForm).toHaveBeenCalled();
    });

    it('handles date change for risk assessment date', async () => {
      render(
        <EditBasicDetailsComp
          clonedForm={mockRiskForm}
          setClonedForm={mockSetClonedForm}
          type="risk"
        />,
      );

      await waitFor(() => {
        const dateInput = screen.getByTestId('date-of-risk-assessment-input');
        fireEvent.change(dateInput, {target: {value: '2024-02-15'}});
      });

      expect(mockSetClonedForm).toHaveBeenCalledWith({
        ...mockRiskForm,
        date_risk_assessment: '2024-02-15',
      });
    });

    it('handles approval required dropdown change (multiple selection)', async () => {
      render(
        <EditBasicDetailsComp
          clonedForm={mockRiskForm}
          setClonedForm={mockSetClonedForm}
          type="risk"
        />,
      );

      await waitFor(() => {
        const approvalSelect = screen.getByTestId(
          'approvals-required-(if-necessary)-select',
        );
        // Simulate selecting an option (our mock handles multiple as single for simplicity)
        fireEvent.change(approvalSelect, {target: {value: '1'}});
      });

      // Check that the function was called (approval logic is tested in the component)
      expect(mockSetClonedForm).toHaveBeenCalled();
    });

    it('shows validation errors for empty risk form fields', () => {
      const emptyRiskForm: RiskForm = {
        ...mockRiskForm,
        task_requiring_ra: '',
        task_duration: '',
        vessel_ownership_id: 0,
        date_risk_assessment: '',
        approval_required: [],
      };

      render(
        <EditBasicDetailsComp
          clonedForm={emptyRiskForm}
          setClonedForm={mockSetClonedForm}
          type="risk"
        />,
      );

      const taskInput = screen.getByLabelText('Task Requiring R.A.');
      const durationInput = screen.getByLabelText('Task Duration');

      expect(taskInput).toHaveClass('is-invalid');
      expect(durationInput).toHaveClass('is-invalid');
    });

    it('displays correct vessel options when assessor is vessel', async () => {
      const vesselAssessorForm = {...mockRiskForm, assessor: 2};
      render(
        <EditBasicDetailsComp
          clonedForm={vesselAssessorForm}
          setClonedForm={mockSetClonedForm}
          type="risk"
        />,
      );

      await waitFor(() => {
        const vesselOfficeSelect = screen.getByTestId('vessel/office-select');
        expect(vesselOfficeSelect).toBeInTheDocument();
        // Check that vessel options are available
        expect(screen.getByText('Vessel 1')).toBeInTheDocument();
        expect(screen.getByText('Vessel 2')).toBeInTheDocument();
      });
    });

    it('displays correct office options when assessor is office', async () => {
      const officeAssessorForm = {...mockRiskForm, assessor: 1};
      render(
        <EditBasicDetailsComp
          clonedForm={officeAssessorForm}
          setClonedForm={mockSetClonedForm}
          type="risk"
        />,
      );

      await waitFor(() => {
        const vesselOfficeSelect = screen.getByTestId('vessel/office-select');
        expect(vesselOfficeSelect).toBeInTheDocument();
        // Check that office options are available
        expect(screen.getByText('Office 1')).toBeInTheDocument();
        expect(screen.getByText('Office 2')).toBeInTheDocument();
      });
    });

    it('handles API errors gracefully', async () => {
      const consoleSpy = jest
        .spyOn(console, 'error')
        .mockImplementation(() => {});
      mockGetVesselsList.mockRejectedValue(new Error('API Error'));
      mockGetOfficesList.mockRejectedValue(new Error('API Error'));
      mockGetApprovalsRequiredList.mockRejectedValue(new Error('API Error'));

      render(
        <EditBasicDetailsComp
          clonedForm={mockRiskForm}
          setClonedForm={mockSetClonedForm}
          type="risk"
        />,
      );

      await waitFor(() => {
        expect(consoleSpy).toHaveBeenCalled();
      });

      consoleSpy.mockRestore();
    });

    it('does not load options for template type', () => {
      render(
        <EditBasicDetailsComp
          clonedForm={mockTemplateForm}
          setClonedForm={mockSetClonedForm}
          type="template"
        />,
      );

      expect(mockGetVesselsList).not.toHaveBeenCalled();
      expect(mockGetOfficesList).not.toHaveBeenCalled();
      expect(mockGetApprovalsRequiredList).not.toHaveBeenCalled();
    });

    it('handles empty date change', async () => {
      render(
        <EditBasicDetailsComp
          clonedForm={mockRiskForm}
          setClonedForm={mockSetClonedForm}
          type="risk"
        />,
      );

      await waitFor(() => {
        const dateInput = screen.getByTestId('date-of-risk-assessment-input');
        fireEvent.change(dateInput, {target: {value: ''}});
      });

      expect(mockSetClonedForm).toHaveBeenCalledWith({
        ...mockRiskForm,
        date_risk_assessment: '',
      });
    });

    it('handles empty approval selection', async () => {
      render(
        <EditBasicDetailsComp
          clonedForm={mockRiskForm}
          setClonedForm={mockSetClonedForm}
          type="risk"
        />,
      );

      await waitFor(() => {
        const approvalSelect = screen.getByTestId(
          'approvals-required-(if-necessary)-select',
        );
        fireEvent.change(approvalSelect, {target: {value: ''}});
      });

      expect(mockSetClonedForm).toHaveBeenCalledWith({
        ...mockRiskForm,
        approval_required: [],
      });
    });
  });

  describe('Field Validation Tests', () => {
    it('enforces maxLength on task requiring RA field', () => {
      render(
        <EditBasicDetailsComp
          clonedForm={mockTemplateForm}
          setClonedForm={mockSetClonedForm}
          type="template"
        />,
      );

      const taskInput = screen.getByLabelText('Task Requiring R.A.');
      expect(taskInput).toHaveAttribute('maxLength', '255');
    });

    it('enforces maxLength on task duration field', () => {
      render(
        <EditBasicDetailsComp
          clonedForm={mockTemplateForm}
          setClonedForm={mockSetClonedForm}
          type="template"
        />,
      );

      const durationInput = screen.getByLabelText('Task Duration');
      expect(durationInput).toHaveAttribute('maxLength', '255');
    });

    it('shows placeholder text for task duration', () => {
      render(
        <EditBasicDetailsComp
          clonedForm={mockTemplateForm}
          setClonedForm={mockSetClonedForm}
          type="template"
        />,
      );

      const durationInput = screen.getByLabelText('Task Duration');
      expect(durationInput).toHaveAttribute(
        'placeholder',
        'Enter No. of Days Required',
      );
    });

    it('shows helper text for task duration', () => {
      render(
        <EditBasicDetailsComp
          clonedForm={mockTemplateForm}
          setClonedForm={mockSetClonedForm}
          type="template"
        />,
      );

      expect(
        screen.getByText('Mention if values are in Days/Hours'),
      ).toBeInTheDocument();
    });
  });
});
