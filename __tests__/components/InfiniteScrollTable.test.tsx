import React from 'react';
import {
  render,
  screen,
  fireEvent,
  waitFor,
  within,
} from '@testing-library/react';
import InfiniteScrollTable from '../../src/components/InfiniteScrollTable';

// Mock IntersectionObserver
beforeAll(() => {
  const mockIntersectionObserver = jest.fn();
  mockIntersectionObserver.mockImplementation(() => ({
    observe: () => null,
    unobserve: () => null,
    disconnect: () => null,
  }));
  window.IntersectionObserver = mockIntersectionObserver;
});

// Mock Element.prototype methods for virtualization
beforeAll(() => {
  Object.defineProperty(HTMLElement.prototype, 'offsetHeight', {
    configurable: true,
    value: 500,
  });
  Object.defineProperty(window, 'innerHeight', {
    configurable: true,
    value: 800,
  });
  Element.prototype.getBoundingClientRect = jest.fn(() => ({
    width: 120,
    height: 40,
    top: 0,
    left: 0,
    bottom: 500,
    right: 120,
    x: 0,
    y: 0,
    toJSON: jest.fn(),
  }));
});

describe('InfiniteScrollTable Component', () => {
  const mockData = [
    {id: 1, name: 'Test Item 1', type: 'Type A', date: '2025-04-15'},
    {id: 2, name: 'Test Item 2', type: 'Type B', date: '2025-04-14'},
  ];

  const mockColumns = [
    {
      id: 'name',
      header: 'Name',
      accessorKey: 'name',
      cell: (info: any) => info.getValue(),
      minSize: 150,
      enableSorting: true,
    },
    {
      id: 'type',
      header: 'Type',
      accessorKey: 'type',
      cell: (info: any) => info.getValue(),
      minSize: 150,
      enableSorting: true,
    },
    {
      id: 'date',
      header: 'Date',
      accessorKey: 'date',
      cell: (info: any) => info.getValue(),
      minSize: 150,
      enableSorting: true,
    },
    {
      id: 'actions',
      header: 'Actions',
      cell: () => <button>Action</button>,
      minSize: 100,
      meta: {isSticky: true},
    },
  ];

  const mockPagination = {
    page: 1,
    pageSize: 10,
    totalItems: 2,
    totalPages: 1,
  };

  const mockSorting = {
    sorting: [],
    onSortingChange: jest.fn(),
  };

  const defaultProps = {
    data: mockData,
    columns: mockColumns,
    isLoading: false,
    isFetchingNextPage: false,
    fetchNextPage: jest.fn(),
    pagination: mockPagination,
    sorting: mockSorting,
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders table with data and headers', () => {
    render(<InfiniteScrollTable {...defaultProps} />);
    expect(screen.getByText('Name')).toBeInTheDocument();
    expect(screen.getByText('Type')).toBeInTheDocument();
    expect(screen.getByText('Date')).toBeInTheDocument();
    expect(screen.getByText('Actions')).toBeInTheDocument();
    expect(screen.getByText('Test Item 1')).toBeInTheDocument();
    expect(screen.getByText('Test Item 2')).toBeInTheDocument();
    expect(screen.getByText('Type A')).toBeInTheDocument();
    expect(screen.getByText('Type B')).toBeInTheDocument();
  });

  it('displays loading spinner when isLoading is true', () => {
    render(<InfiniteScrollTable {...defaultProps} isLoading={true} />);
    expect(screen.getAllByText(/loading/i).length).toBeGreaterThan(0);
  });

  it('displays no results message when data is empty', () => {
    render(<InfiniteScrollTable {...defaultProps} data={[]} />);
    expect(screen.getByText(/no result/i)).toBeInTheDocument();
  });

  it('handles sorting when clicking column headers', () => {
    const onSortingChange = jest.fn();
    render(
      <InfiniteScrollTable
        {...defaultProps}
        sorting={{sorting: [], onSortingChange}}
      />,
    );
    const nameHeader = screen.getAllByRole('columnheader')[0];
    fireEvent.click(nameHeader);
    expect(onSortingChange).toHaveBeenCalled();
    expect(within(nameHeader).getByTestId('sort-icon')).toBeInTheDocument();
  });

  it('renders sticky actions column', () => {
    render(<InfiniteScrollTable {...defaultProps} />);
    const headers = screen.getAllByRole('columnheader');
    const actionsHeader = headers[headers.length - 1];
    expect(actionsHeader).toHaveClass('sticky-styles-right');
    const actionButtons = screen.getAllByRole('button');
    actionButtons.forEach(button => {
      const cell = button.closest('td');
      expect(cell).toHaveClass('sticky-styles-right');
    });
  });

  it('fetches more data when scrolled to bottom and more pages exist', async () => {
    const mockFetchNextPage = jest.fn();
    const {container} = render(
      <InfiniteScrollTable
        {...defaultProps}
        pagination={{...mockPagination, page: 1, totalPages: 2}}
        fetchNextPage={mockFetchNextPage}
      />,
    );
    const tableContainer = container.querySelector('.table-responsive');
    Object.defineProperties(tableContainer!, {
      scrollHeight: {value: 1000, configurable: true},
      clientHeight: {value: 500, configurable: true},
      scrollTop: {value: 450, configurable: true},
    });
    fireEvent.scroll(tableContainer!);
    await waitFor(() => {
      expect(mockFetchNextPage).toHaveBeenCalled();
    });
  });

  it('does not fetch more data when at the last page', async () => {
    const mockFetchNextPage = jest.fn();
    const {container} = render(
      <InfiniteScrollTable
        {...defaultProps}
        pagination={{...mockPagination, page: 1, totalPages: 1}}
        fetchNextPage={mockFetchNextPage}
      />,
    );
    const tableContainer = container.querySelector('.table-responsive');
    Object.defineProperties(tableContainer!, {
      scrollHeight: {value: 1000, configurable: true},
      clientHeight: {value: 500, configurable: true},
      scrollTop: {value: 450, configurable: true},
    });
    fireEvent.scroll(tableContainer!);
    await new Promise(res => setTimeout(res, 100));
    expect(mockFetchNextPage).not.toHaveBeenCalled();
  });

  it('does not fetch more data when already fetching', async () => {
    const mockFetchNextPage = jest.fn();
    const {container} = render(
      <InfiniteScrollTable
        {...defaultProps}
        isFetchingNextPage={true}
        pagination={{...mockPagination, page: 1, totalPages: 2}}
        fetchNextPage={mockFetchNextPage}
      />,
    );
    const tableContainer = container.querySelector('.table-responsive');
    Object.defineProperties(tableContainer!, {
      scrollHeight: {value: 1000, configurable: true},
      clientHeight: {value: 500, configurable: true},
      scrollTop: {value: 450, configurable: true},
    });
    fireEvent.scroll(tableContainer!);
    await new Promise(res => setTimeout(res, 100));
    expect(mockFetchNextPage).not.toHaveBeenCalled();
  });

  it('handles the case when all content fits in view and more pages exist', async () => {
    const mockFetchNextPage = jest.fn();
    const {container} = render(
      <InfiniteScrollTable
        {...defaultProps}
        pagination={{...mockPagination, page: 1, totalPages: 2, pageSize: 10}}
        data={mockData}
        fetchNextPage={mockFetchNextPage}
      />,
    );
    const tableContainer = container.querySelector('.table-responsive');
    Object.defineProperties(tableContainer!, {
      scrollHeight: {value: 500, configurable: true},
      clientHeight: {value: 500, configurable: true},
    });
    fireEvent.scroll(tableContainer!);
    await waitFor(() => {
      expect(mockFetchNextPage).toHaveBeenCalled();
    });
  });

  it('uses correct CSS classes for column styling', () => {
    render(<InfiniteScrollTable {...defaultProps} />);
    const headers = screen.getAllByRole('columnheader');
    headers.forEach(header => {
      expect(header).toHaveClass('border-top-0');
    });
    const lastHeader = headers[headers.length - 1];
    expect(lastHeader).toHaveClass('sticky-styles-right');
  });

  it('applies virtualization to rows', () => {
    const manyRows = Array.from({length: 100}, (_, i) => ({
      id: i + 1,
      name: `Test Item ${i + 1}`,
      type: i % 2 === 0 ? 'Type A' : 'Type B',
      date: '2025-04-15',
    }));
    render(
      <InfiniteScrollTable
        {...defaultProps}
        data={manyRows}
        pagination={{...mockPagination, totalItems: 100, totalPages: 10}}
      />,
    );
    expect(screen.getByText('Test Item 1')).toBeInTheDocument();
    const rows = screen
      .getAllByRole('row')
      .filter(row => !row.querySelector('th'));
    expect(rows.length).toBeGreaterThan(0);
  });
});
