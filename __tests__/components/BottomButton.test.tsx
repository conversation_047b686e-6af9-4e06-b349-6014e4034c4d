import React from 'react';
import {render, screen, fireEvent} from '@testing-library/react';
import BottomButton, {ButtonConfig} from '../../src/components/BottomButton';

describe('BottomButton', () => {
  const mockClickHandler = jest.fn();

  const buttons: ButtonConfig[] = [
    {
      title: 'Submit',
      testID: 'submit-button',
      variant: 'success',
      onClick: mockClickHandler,
      type: 'submit',
      disabled: false,
      customClass: 'custom-submit',
    },
    {
      title: 'Cancel',
      testID: 'cancel-button',
      variant: 'secondary',
      onClick: mockClickHandler,
      type: 'button',
      disabled: true,
    },
  ];

  beforeEach(() => {
    mockClickHandler.mockClear();
  });

  it('renders the correct number of buttons', () => {
    render(<BottomButton buttons={buttons} />);
    const renderedButtons = screen.getAllByRole('button');
    expect(renderedButtons).toHaveLength(buttons.length);
  });

  it('renders button titles correctly', () => {
    render(<BottomButton buttons={buttons} />);
    expect(screen.getByText('Submit')).toBeInTheDocument();
    expect(screen.getByText('Cancel')).toBeInTheDocument();
  });

  it('calls onClick handler when button is clicked', () => {
    render(<BottomButton buttons={buttons} />);
    const submitBtn = screen.getByTestId('submit-button');
    fireEvent.click(submitBtn);
    expect(mockClickHandler).toHaveBeenCalledTimes(1);
  });

  it('does not call onClick handler when disabled button is clicked', () => {
    render(<BottomButton buttons={buttons} />);
    const cancelBtn = screen.getByTestId('cancel-button');
    fireEvent.click(cancelBtn);
    expect(mockClickHandler).toHaveBeenCalledTimes(0); // already cleared in beforeEach
  });

  it('applies custom class correctly', () => {
    render(<BottomButton buttons={buttons} />);
    const submitBtn = screen.getByTestId('submit-button');
    expect(submitBtn).toHaveClass('bottom-component__button custom-submit');
  });

  it('applies the correct button type and variant', () => {
    render(<BottomButton buttons={buttons} />);
    const submitBtn = screen.getByTestId('submit-button');
    expect(submitBtn).toHaveAttribute('type', 'submit');
    expect(submitBtn).toHaveClass('btn-success');

    const cancelBtn = screen.getByTestId('cancel-button');
    expect(cancelBtn).toHaveAttribute('type', 'button');
    expect(cancelBtn).toHaveClass('btn-secondary');
  });

  it('applies disabled attribute when specified', () => {
    render(<BottomButton buttons={buttons} />);
    const cancelBtn = screen.getByTestId('cancel-button');
    expect(cancelBtn).toBeDisabled();
  });

  it('uses "primary" as default variant when none is provided', () => {
    const buttonWithNoVariant: ButtonConfig[] = [
      {
        title: 'Default Variant',
        testID: 'default-variant-btn',
        onClick: jest.fn(),
      },
    ];
    render(<BottomButton buttons={buttonWithNoVariant} />);
    const button = screen.getByTestId('default-variant-btn');
    expect(button).toHaveClass('btn-primary');
  });

  it('uses "button" as default type when none is provided', () => {
    const buttonWithNoType: ButtonConfig[] = [
      {title: 'Default Type', testID: 'default-type-btn', onClick: jest.fn()},
    ];
    render(<BottomButton buttons={buttonWithNoType} />);
    const button = screen.getByTestId('default-type-btn');
    expect(button).toHaveAttribute('type', 'button');
  });
});
