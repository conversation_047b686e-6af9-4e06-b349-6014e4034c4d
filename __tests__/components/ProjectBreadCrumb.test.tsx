import React from 'react';
import {render, screen} from '@testing-library/react';
import {MemoryRouter} from 'react-router-dom';
import '@testing-library/jest-dom';
import ProjectBreadCrumb from '../../src/components/ProjectBreadCrumb';

describe('ProjectBreadCrumb', () => {
  const renderWithRouter = (ui: React.ReactElement) => {
    return render(ui, {wrapper: MemoryRouter});
  };

  it('renders breadcrumb without links when none are provided', () => {
    const items = [{title: 'Step One'}, {title: 'Step Two'}];

    renderWithRouter(<ProjectBreadCrumb items={items} />);

    items.forEach(item => {
      const element = screen.getByText(item.title);
      expect(element.tagName).toBe('SPAN');
    });

    expect(screen.queryByRole('link')).not.toBeInTheDocument();
  });

  it('applies correct text and link styles', () => {
    const items = [{title: 'Dashboard', link: '/dashboard'}];

    renderWithRouter(<ProjectBreadCrumb items={items} />);
    const link = screen.getByText('Dashboard');

    expect(link).toHaveStyle('color: #1F4A70');
    expect(link).toHaveStyle('text-decoration: underline');
    expect(link).toHaveClass('fs-20');
  });

  it('preserves state in the link if provided', () => {
    const state = {from: 'search'};
    const items = [{title: 'Search Results', link: '/search', state}];

    renderWithRouter(<ProjectBreadCrumb items={items} />);

    const link = screen.getByText('Search Results');
    expect(link).toBeInTheDocument();

    // Can't inspect `state` directly, but you can test link presence
    expect(link.closest('a')).toHaveAttribute('href', '/search');
  });

  it('renders correctly with only one item', () => {
    const items = [{title: 'Only Item'}];
    renderWithRouter(<ProjectBreadCrumb items={items} />);

    const text = screen.getByText('Only Item');
    expect(text).toBeInTheDocument();
    expect(text.tagName).toBe('SPAN');
    expect(screen.queryByText('/')).not.toBeInTheDocument();
  });
});
