import React from 'react';
import {render, screen} from '@testing-library/react';
import {MemoryRouter} from 'react-router-dom';
import AppRoutes from '../src/routes/rootRoutes';
import mockedRoutesConfig from '../src/routes/route.config';
import {useDataStoreContext} from '../src/context';

// Mock useDataStoreContext
jest.mock('../src/context', () => ({
  useDataStoreContext: jest.fn(),
}));

// Mock routesConfig and IRoute
const DummyComponent = () => <div>DummyComponent</div>;
const DummyChild = () => <div>DummyChild</div>;

jest.mock('../src/routes/route.config', () => {
  return {
    __esModule: true,
    default: jest.fn(),
    IRoute: {},
  };
});

describe('AppRoutes', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders routes with components', () => {
    mockedRoutesConfig.mockReturnValue([
      {
        path: '/dummy',
        component: DummyComponent,
      },
    ]);
    useDataStoreContext.mockReturnValue({roleConfig: {}});

    render(
      <MemoryRouter initialEntries={['/dummy']}>
        <AppRoutes />
      </MemoryRouter>,
    );

    expect(screen.getByText('DummyComponent')).toBeInTheDocument();
  });

  it('renders redirect route', () => {
    mockedRoutesConfig.mockReturnValue([
      {
        path: '/redirect',
        redirect: '/dummy',
      },
      {
        path: '/dummy',
        component: DummyComponent,
      },
    ]);
    useDataStoreContext.mockReturnValue({roleConfig: {}});

    render(
      <MemoryRouter initialEntries={['/redirect']}>
        <AppRoutes />
      </MemoryRouter>,
    );

    // Should redirect to /dummy and render DummyComponent
    expect(screen.getByText('DummyComponent')).toBeInTheDocument();
  });

  it('redirects when isPermission is false', () => {
    mockedRoutesConfig.mockReturnValue([
      {
        path: '/forbidden',
        isPermission: false,
      },
      {
        path: '/vessel',
        component: DummyComponent,
      },
    ]);
    useDataStoreContext.mockReturnValue({roleConfig: {}});

    render(
      <MemoryRouter initialEntries={['/forbidden']}>
        <AppRoutes />
      </MemoryRouter>,
    );

    // Should redirect to /vessel and render DummyComponent
    expect(screen.getByText('DummyComponent')).toBeInTheDocument();
  });

  it('renders Not Found for unknown routes', () => {
    mockedRoutesConfig.mockReturnValue([]);
    useDataStoreContext.mockReturnValue({roleConfig: {}});

    render(
      <MemoryRouter initialEntries={['/unknown']}>
        <AppRoutes />
      </MemoryRouter>,
    );

    expect(screen.getByText('Not Found!')).toBeInTheDocument();
  });

  it('shows fallback while loading', () => {
    // Simulate a lazy component
    const LazyComponent = React.lazy(() =>
      Promise.resolve({default: DummyComponent}),
    );
    mockedRoutesConfig.mockReturnValue([
      {
        path: '/lazy',
        component: LazyComponent,
      },
    ]);
    useDataStoreContext.mockReturnValue({roleConfig: {}});

    render(
      <MemoryRouter initialEntries={['/lazy']}>
        <AppRoutes />
      </MemoryRouter>,
    );

    expect(screen.getByText('Loading.....')).toBeInTheDocument();
  });
});
