const {merge} = require('webpack-merge');
const singleSpaDefaults = require('webpack-config-single-spa-react-ts');
// webpack.config.js
const StandaloneSingleSpaPlugin = require('standalone-single-spa-webpack-plugin');
const HtmlWebpackPlugin = require('html-webpack-plugin');
const webpack = require('webpack');

require('dotenv').config({path: './paris2-configuration.env'});

module.exports = (webpackConfigEnv, argv) => {
  const defaultConfig = singleSpaDefaults({
    orgName: 'paris2',
    projectName: 'risk-assessment',
    webpackConfigEnv,
    argv,
  });

  const data = merge(defaultConfig, {
    // modify the webpack config however you'd like to by adding to this object
    plugins: [
      new webpack.DefinePlugin({
        'process.env': JSON.stringify(process.env),
      }),
    ],
    module: {
      rules: [
        {
          test: /\.s[ac]ss$/i,
          use: [
            // Creates `style` nodes from JS strings
            {
              loader: 'style-loader',
              options: {
                insert: function insertStyle(element) {
                  var parent = document.querySelector('#paris2-inline-style');
                  if (parent) {
                    element.setAttribute('nonce', parent.getAttribute('nonce'));
                    parent.appendChild(element);
                  } else {
                    var head = document.querySelector('head');
                    head.appendChild(element);
                  }
                },
              },
            },
            // Translates CSS into CommonJS
            'css-loader',
            // Compiles Sass to CSS
            'sass-loader',
          ],
        },
        {
          test: /\.svg$/,
          use: [
            {
              loader: 'svg-url-loader',
              options: {
                limit: 10000,
              },
            },
          ],
        },
        {
          test: /\.(ttf|eot|woff|woff2)$/,
          use: {
            loader: 'file-loader',
            options: {
              name: '[name].[ext]',
            },
          },
        },
        {
          test: /\.(png|jpeg|gif|pdf)$/,
          use: {
            loader: 'file-loader',
            options: {
              name: '[name].[ext]',
            },
          },
        },
      ],
    },
    externals: ['single-spa', new RegExp(`^@paris2/*`)],
  });
  return data;
};
