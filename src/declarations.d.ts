declare module '*.html' {
  const rawHtmlFile: string;
  export = rawHtmlFile;
}

const src: string;
declare module '*.bmp' {
  export default src;
}

declare module '*.gif' {
  export default src;
}

declare module '*.jpg' {
  export default src;
}

declare module '*.jpeg' {
  export default src;
}

declare module '*.png' {
  export default src;
}

declare module '*.webp' {
  export default src;
}

declare module '*.svg' {
  export default src;
}

declare module '@paris2/auth' {
  export function init(): Promise<void>;
  export function getToken(): Promise<string>;
}

declare module '@paris2/styleguide' {
  export function Icon(props: any): JSX.Element;
  export function ErrorPage(props: any): JSX.Element;
}

declare module 'pdfjs-dist/build/pdf.worker.entry' {
  const workerSrc: string;

  export default workerSrc;
}
