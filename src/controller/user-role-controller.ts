import {JwtUser} from '../types/user';
import {RISK_ASSESMENT_ROLES as ROLES} from '../constants/roles';

class UserRoleController {
  getConfig(kc: any) {
    const hasRole = (role: string): boolean =>
      kc.realmAccess.roles.includes(role);

    return {
      user: kc.tokenParsed as JwtUser,
      riskAssessment: {
        hasAdminAccess: hasRole(ROLES.HAS_ADMIN_ACCESS),
        canCreateNewTemplate: hasRole(ROLES.HAS_RA_CREATE_TEMPLATE_ACCESS),
        // add all the required permission for new building
        hasPermision: hasRole(ROLES.HAS_RA_VIEW_ACCESS),
        canExportPDF: hasRole(ROLES.HAS_PDF_EXPORT_ACCESS),
        canAddRALvl1: hasRole(ROLES.HAS_RA_LEVEL_1_CREATE_ACCESS),
        canViewTemplate: hasRole(ROLES.HAS_TEMPLATE_VIEW_ACCESS),
        canArchiveTemplate: hasRole(ROLES.HAS_TEMPLATE_ARCHIVE_ACCESS),
        canApproveRisk: hasRole(ROLES.HAS_RISK_APPROVAL_ACCESS),
      },
    };
  }
}

export default UserRoleController;
