import React, {Suspense} from 'react';
import {Navigate, Route, Routes} from 'react-router-dom';
import {useDataStoreContext} from '../context';
import routesConfig, {IRoute} from './route.config';

const RouteGenerator = (routeConfig: IRoute, key: number | string) => {
  const {
    redirect,
    path,
    childRoutes,
    component: Component,
    isPermission = null,
  } = routeConfig;

  if (isPermission === false)
    return (
      <Route path={path} key={key} element={<Navigate to={'/vessel'} />} />
    );

  if (redirect) {
    return <Route path={path} key={key} element={<Navigate to={redirect} />} />;
  } else if (Component) {
    if (childRoutes)
      return (
        <Route key={key} path={path} element={<Component />}>
          {childRoutes.map((childRoute, key2) =>
            RouteGenerator(childRoute, `${key}${key2}`),
          )}
        </Route>
      );
    else return <Route key={key} path={path} element={<Component />} />;
  }
};

const AppRoutes = () => {
  const {roleConfig} = useDataStoreContext();

  return (
    <Suspense fallback={'Loading.....'}>
      <Routes>
        {routesConfig(roleConfig).map((routeConfig, key) =>
          RouteGenerator(routeConfig, key),
        )}
        <Route path="*" element={'Not Found!'} />
      </Routes>
    </Suspense>
  );
};

export default AppRoutes;
