import {RaLevel} from '../enums';
import {DefaultAPIFields, Parameter, RiskRating} from './template';
export interface RiskListResponse {
  message: string;
  result: {
    data: RiskItem[];
    pagination: Pagination;
  };
}

export interface RiskItem {
  id: number;
  template_id: number | null;
  task_requiring_ra: string;
  assessor: number;
  vessel_ownership_id: number | null;
  vessel_id: number | null;
  vessel_code: string | null;
  vessel_name: string | null;
  vessel_tech_group: string | null;
  vessel_category: string | null;
  office_id: number | null;
  office_name: string | null;
  date_risk_assessment: string;
  task_duration: string;
  ra_level: number | null | undefined;
  task_alternative_consideration: string;
  task_rejection_reason: string;
  worst_case_scenario: string;
  recovery_measures: string;
  status: number;
  publish_on: string;
  approval_date: string | null;
  draft_step: number | null;
  created_by: string;
  updated_by: string | null;
  deleted_at: string | null;
  deleted_by: string | null;
  created_at: string;
  updated_at: string;
}

export interface Pagination {
  totalItems: number;
  totalPages: number;
  page: number;
  pageSize: number;
}

// Risk Form interfaces
export interface RiskTeamMember {
  seafarer_id: number;
  seafarer_person_id: number;
  seafarer_hkid: number;
  seafarer_rank_id: number;
  seafarer_name: string;
  seafarer_rank: string;
  seafarer_rank_sort_order: number;
  keycloak_id?: string;
  user_name?: string;
  rank?: string;
  email?: string;
}

export interface RiskCategory {
  is_other: boolean;
  category_id: number[];
  value: string;
}

export interface RiskHazard {
  is_other: boolean;
  hazard_id: number[];
  value: string;
}

export interface RiskJobInitialRiskRating {
  parameter_type_id: number;
  rating: string;
}

export interface RiskJobResidualRiskRating {
  parameter_type_id: number;
  rating: string;
  reason: string;
}

// Form-specific interface for risk jobs (similar to TemplateFormJob)
export interface RiskFormJob {
  job_step: string;
  job_hazard: string;
  job_nature_of_risk: string;
  job_additional_mitigation: string;
  job_close_out_date: string;
  job_existing_control: string;
  job_close_out_responsibility_id: string;
  risk_job_initial_risk_rating: RiskJobInitialRiskRating[];
  risk_job_residual_risk_rating: RiskJobResidualRiskRating[];
}

// Database entity interface for risk jobs
export interface RiskJob {
  id: number;
  risk_id: number;
  job_step: string;
  job_hazard: string;
  job_nature_of_risk: string;
  job_additional_mitigation: string;
  job_close_out_date: string;
  job_existing_control: string;
  job_close_out_responsibility_id: string;
  risk_job_initial_risk_rating: RiskRating[];
  risk_job_residual_risk_rating: RiskRating[];
  status: number;
  created_by: string;
  updated_by: string | null;
  deleted_at: string | null;
  deleted_by: string | null;
  createdAt: string;
  updatedAt: string;
}

export interface RiskTaskReliabilityAssessment {
  task_reliability_assessment_id: number;
  task_reliability_assessment_answer: string;
  condition: string;
}

export interface RiskForm extends DefaultAPIFields {
  template_id?: number;
  task_requiring_ra: string;
  assessor: number;
  vessel_ownership_id: number;
  vessel_id?: number;
  date_risk_assessment: string;
  task_duration: string;
  task_alternative_consideration: string;
  task_rejection_reason: string;
  worst_case_scenario: string;
  recovery_measures: string;
  status: string;
  approval_required: number[];
  risk_team_member: RiskTeamMember[];
  risk_category: RiskCategory;
  risk_hazard: RiskHazard;
  parameters: Parameter[];
  risk_job: RiskFormJob[];
  risk_task_reliability_assessment: RiskTaskReliabilityAssessment[];
  ra_level: RaLevel | undefined;
  approval_date: string | undefined;
  risk_approver: RAItemFull['risk_approver'] | undefined;
  office_id: number;
  office_name: string;
}

export type RAItemFull = {
  id: number;
  template_id: number | null;
  task_requiring_ra: string;
  assessor: number;
  vessel_ownership_id: number | null;
  vessel_id: number | null;
  vessel_code: string | null;
  vessel_name: string | null;
  vessel_tech_group: string | null;
  vessel_category: string | null;
  office_id: number | null;
  office_name: string | null;
  date_risk_assessment: string;
  task_duration: string | null;
  ra_level: string | null;
  task_alternative_consideration: string | null;
  task_rejection_reason: string | null;
  worst_case_scenario: string | null;
  recovery_measures: string | null;
  status: number;
  publish_on: string | null;
  approval_date: string | null;
  draft_step: number | null;
  created_by: string;
  updated_by: string | null;
  deleted_at: string | null;
  deleted_by: string | null;
  created_at: string;
  updated_at: string;

  risk_team_member: Array<{
    id: number;
    risk_id: number;
    seafarer_id: number;
    seafarer_person_id: number;
    seafarer_hkid: number;
    seafarer_name: string;
    seafarer_rank: string;
    seafarer_rank_id: number;
    seafarer_rank_sort_order: number;
  }>;

  risk_category: Array<{
    id: number;
    category: {
      id: number;
      name: string;
    };
  }>;

  risk_hazards: Array<{
    id: number;
    hazard_category_is_other: boolean;
    value: string | null;
    hazard_detail: {
      id: number;
      name: string;
    } | null;
  }>;

  risk_parameter: Array<{
    id: number;
    parameter_is_other: boolean;
    value: string | null;
    parameterType: {
      id: number;
      name: string;
    };
    parameter: {
      id: number;
      name: string;
    } | null;
  }>;

  risk_job: Array<{
    id: number;
    risk_id: number;
    job_step: string;
    job_hazard: string;
    job_nature_of_risk: string;
    job_existing_control: string;
    job_additional_mitigation: string;
    job_close_out_date: string;
    job_close_out_responsibility_id: string | number;
    risk_job_initial_risk_rating: Array<{
      id: number;
      risk_job_id: number;
      parameter_type_id: number;
      rating: string;
    }>;
    risk_job_residual_risk_rating: Array<{
      id: number;
      risk_job_id: number;
      parameter_type_id: number;
      rating: string;
      reason: string;
    }>;
  }>;

  risk_approver: Array<{
    id: number;
    risk_id: number;
    keycloak_id: string;
    user_name: string;
    user_email: string;
    job_title: string | null;
    message: string | null;
    approval_order: number | null;
    approval_status: number | null;
    approval_date: string | null;
    status: number;
  }>;

  risk_approval_required: Array<{
    id: number;
    approval_required: {
      name: string;
      id: number;
    };
  }>;

  risk_task_reliability_assessment: Array<{
    id: number;
    risk_id: number;
    task_reliability_assessment_id: number;
    task_reliability_assessment_answer: string;
    condition: string;
  }>;
};
