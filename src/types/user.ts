interface RealmAccess {
  roles: string[];
}

interface ResourceAccess {
  account: RealmAccess;
}

export interface BasicUserDetails {
  userId: string;
  email: string;
  full_name: string;
  designation?: string;
}

export interface JwtUser {
  exp: number;
  iat: number;
  auth_time: number;
  jti: string;
  iss: string;
  aud: string;
  sub: string;
  typ: string;
  azp: string;
  nonce: string;
  session_state: string;
  acr: string;
  'allowed-origins': string[];
  realm_access: RealmAccess;
  resource_access: ResourceAccess;
  scope: string;
  user_name_hash: string;
  email_verified: boolean;
  is_nova_onboarded: boolean;
  user_id: string;
  name: string;
  tc_nova_version: number;
  is_user_onboarded: boolean;
  preferred_username: string;
  given_name: string;
  family_name: string;
  email: string | null;
  group: string[];
}
