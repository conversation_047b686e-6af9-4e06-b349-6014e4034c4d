export const RISK_RATING_NOTE = {
  HIGH: `Work cannot commence at this level unless risk is accepted. Considerable efforts must be made to implement measures to reduce the risk level within a defined time period.`,
  MEDIUM: `Considerable efforts must be made to implement measures to reduce the risk level to LOW. Any additional measures shall be implemented within a defined time period. Monitor to ensure that existing controls are maintained.`,
  LOW: `No additional controls are required, these risks are considered acceptable. Monitored to ensure that existing controls are maintained.`,
};

export const initialRiskRatingField = (type: 'template' | 'risk') =>
  type === 'risk'
    ? 'risk_job_initial_risk_rating'
    : 'template_job_initial_risk_rating';
export const residualRiskRatingField = (type: 'template' | 'risk') =>
  type === 'risk'
    ? 'risk_job_residual_risk_rating'
    : 'template_job_residual_risk_rating';

export const RACategoryRiskFormOption = [
  {
    label: 'Regular RA',
    value: undefined,
  },
  {
    label: 'Level 1 RA',
    value: 4,
  },
];
