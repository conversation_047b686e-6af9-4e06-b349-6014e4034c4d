import React, {useEffect, useState} from 'react';
import {<PERSON><PERSON><PERSON><PERSON>out<PERSON>} from 'react-router-dom';
import DataStoreProvider from './context/DataStoreProvider';
import UserRoleController from './controller/user-role-controller';
import AppRoutes from './routes/rootRoutes';
import userService from './services/user.service';
import {UserRoleControllerConfig} from './types/controller';
import AlertContextProvider from './context/AlertContextProvider';
import './styles/global.scss';
import {ToastContainer} from 'react-toastify';

const userRoleController = new UserRoleController();

interface Props {
  kc: any;
  ga4react: any;
}

const Root: React.FC<Props> = ({kc, ga4react}) => {
  const [roleConfig, setRoleConfig] = useState<UserRoleControllerConfig | null>(
    null,
  );

  useEffect(() => {
    const initializeUserService = async () => {
      await userService.init();
      setRoleConfig(config => config || userRoleController.getConfig(kc));
    };

    initializeUserService();
  }, [kc]);

  if (!roleConfig) {
    return (
      <div className="d-flex justify-content-center">
        <output className="spinner-border text-secondary">
          <span className="sr-only">Loading...</span>
        </output>
      </div>
    );
  }

  const ga4EventTrigger = (action: string, label: string, category: string) => {
    try {
      ga4react?.event(action, label, category, false);
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('GA4 Event Trigger Error:', error);
    }
  };

  return (
    <BrowserRouter>
      <AlertContextProvider>
        <DataStoreProvider
          roleConfig={roleConfig}
          ga4EventTrigger={ga4EventTrigger}
        >
          <AppRoutes />
          <ToastContainer />
        </DataStoreProvider>
      </AlertContextProvider>
    </BrowserRouter>
  );
};

export default Root;
