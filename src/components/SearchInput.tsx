import React from 'react';
import {Form} from 'react-bootstrap';
import {X} from 'react-bootstrap-icons';
import {IoIosSearch} from 'react-icons/io';

type SearchInputProps = {
  placeholder?: string;
  onSearch: (query: string | null) => void;
  value: string;
  disabled?: boolean;
  showClear?: boolean;
  onClear?: () => void;
};

const SearchInput: React.FC<SearchInputProps> = ({
  placeholder = 'Search',
  onSearch,
  value,
  disabled = false,
  showClear = false,
  onClear,
}) => {
  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    let newValue = event.target.value;
    // Remove all '%' and '_' characters
    newValue = newValue.replace(/[%_]/g, '');
    if (!newValue.trim()) {
      onSearch(null);
    } else {
      onSearch(newValue);
    }
  };

  return (
    <div className="typeahead-wrapper" style={{position: 'relative'}}>
      <Form.Group>
        <div
          className="clear-icon"
          style={{
            position: 'absolute',
            left: '10px',
            top: '50%',
            transform: 'translateY(-50%)',
            zIndex: 4,
            pointerEvents: 'none', // So it doesn’t block typing
          }}
        >
          {/* eslint-disable-next-line @typescript-eslint/ban-ts-comment */}
          {/* @ts-ignore */}
          {<IoIosSearch size={18} />}
        </div>
        <Form.Control
          type="text"
          value={value}
          placeholder={placeholder}
          onChange={handleInputChange}
          disabled={disabled}
          className="search-input"
        />
        {showClear && (
          <div className="clear-icon" data-testid="clear-icon">
            <button
              type="button"
              className="cursor-pointer unset"
              onClick={() => {
                onSearch('');
                onClear?.();
              }}
            >
              <X data-testid="cross-icon" size={18} />
            </button>
          </div>
        )}
      </Form.Group>
    </div>
  );
};

export default SearchInput;
