import React, {useState} from 'react';
import {Button} from 'react-bootstrap';
import SearchInput from './SearchInput';
import CheckboxComponent from './CheckboxComponent';
import {useDataStoreContext} from '../context';
import TruncateText from './TruncateBasicText';
import Drawer from './Drawer';
import {PlusIcon} from './icons';

import '../styles/components/categories-filters-drawer.scss';

type CategoriesFiltersType = {
  ra_categories?: number[] | null;
  hazard_categories?: number[] | null;
};

interface CategoriesFiltersDrawerProps {
  filters: CategoriesFiltersType;
  onFilterChange: (
    key: keyof CategoriesFiltersType,
    value: number[] | null,
  ) => void;
}

export const CategoriesFiltersDrawer: React.FC<CategoriesFiltersDrawerProps> =
  ({filters, onFilterChange}) => {
    const [raChecked, setRaChecked] = useState<number[]>(
      filters.ra_categories || [],
    );
    const [hazardChecked, setHazardChecked] = useState<number[]>(
      filters.hazard_categories || [],
    );

    const handleClearFilters = () => {
      onFilterChange('ra_categories', null);
      onFilterChange('hazard_categories', null);
      setRaChecked([]);
      setHazardChecked([]);
    };

    const handleUpdateFilters = () => {
      onFilterChange('ra_categories', raChecked);
      onFilterChange('hazard_categories', hazardChecked);
    };

    const handleResetFilters = () => {
      setRaChecked(filters.ra_categories || []);
      setHazardChecked(filters.hazard_categories || []);
    };

    const filterSelectionCount =
      (filters.ra_categories?.length ?? 0) +
      (filters.hazard_categories?.length ?? 0);

    return (
      <Drawer
        title="All Filters"
        trigger={
          <Button variant="outline-primary" className="more-filters-button">
            <PlusIcon className="icon" />
            {filterSelectionCount > 0 ? (
              <span className="label">
                Filters{' '}
                <span className="label-badge">{filterSelectionCount}</span>
              </span>
            ) : (
              <span className="label">More Filters</span>
            )}
          </Button>
        }
        onClose={handleResetFilters}
      >
        {(props: {closeDrawer: () => void}) => (
          <main className="ra-more-filters-content">
            <CategoryFilter checked={raChecked} setChecked={setRaChecked} />
            <HazardFilter
              checked={hazardChecked}
              setChecked={setHazardChecked}
            />
            <div className="filters-footer">
              <Button
                variant="link"
                className="footer-btn-secondary"
                onClick={() => {
                  handleClearFilters();
                  props.closeDrawer();
                }}
              >
                Clear
              </Button>
              <Button
                variant="primary"
                className="footer-btn-primary"
                onClick={() => {
                  handleUpdateFilters();
                  props.closeDrawer();
                }}
              >
                Apply
              </Button>
            </div>
          </main>
        )}
      </Drawer>
    );
  };

export default CategoriesFiltersDrawer;

interface CategoryFilterProps {
  checked: number[];
  setChecked: React.Dispatch<React.SetStateAction<number[]>>;
}

const CategoryFilter: React.FC<CategoryFilterProps> = ({
  checked,
  setChecked,
}) => {
  const {
    dataStore: {riskCategoryList},
  } = useDataStoreContext();
  const [search, setSearch] = useState<string>('');

  const filteredRiskCategories = riskCategoryList.filter(opt =>
    opt.name.toLowerCase().includes(search.toLowerCase()),
  );

  const finalRiskCategoryList = search
    ? filteredRiskCategories
    : riskCategoryList;

  return (
    <div className="filter-container">
      <div className="filter-heading">Filter by R.A. Categories</div>
      <div className="filter-container-content">
        <div className="mb-3">
          <SearchInput
            placeholder="Search RA Category"
            value={search}
            onSearch={e => setSearch(e || '')}
          />
        </div>
        <div className="filter-checkbox-container">
          {finalRiskCategoryList.length > 0 ? (
            finalRiskCategoryList.map(opt => (
              <CheckboxComponent
                key={opt.id}
                id={`category-checked-${opt.id}`}
                checked={checked.includes(opt.id)}
                label={<TruncateText text={opt.name} maxLength={45} />}
                onChange={() =>
                  setChecked(prev =>
                    prev.includes(opt.id)
                      ? prev.filter(o => o !== opt.id)
                      : [...prev, opt.id],
                  )
                }
              />
            ))
          ) : (
            <div className="empty-list-message">
              {search
                ? `No results found for '${search}'.`
                : 'No categories found.'}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

const HazardFilter: React.FC<CategoryFilterProps> = ({checked, setChecked}) => {
  const {
    dataStore: {hazardsList},
  } = useDataStoreContext();
  const [search, setSearch] = useState<string>('');

  const filteredHazards = hazardsList.filter(opt =>
    opt.name.toLowerCase().includes(search.toLowerCase()),
  );
  const finalHazardsList = search ? filteredHazards : hazardsList;

  return (
    <div className="filter-container">
      <div className="filter-heading">Filter by Hazard Categories</div>
      <div className="filter-container-content">
        <div className="mb-3">
          <SearchInput
            placeholder="Search Hazard Categories"
            value={search}
            onSearch={e => setSearch(e || '')}
          />
        </div>
        <div className="filter-checkbox-container">
          {finalHazardsList.length > 0 ? (
            finalHazardsList.map(opt => (
              <CheckboxComponent
                key={opt.id}
                id={`hazard-checked-${opt.id}`}
                checked={checked.includes(opt.id)}
                label={<TruncateText text={opt.name} maxLength={45} />}
                onChange={() =>
                  setChecked(prev =>
                    prev.includes(opt.id)
                      ? prev.filter(o => o !== opt.id)
                      : [...prev, opt.id],
                  )
                }
              />
            ))
          ) : (
            <div className="empty-list-message">
              {search
                ? `No results found for '${search}'.`
                : 'No hazard categories found.'}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
