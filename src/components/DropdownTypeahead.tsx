import React, {useRef} from 'react';
import {Typeahead, MenuItem} from 'react-bootstrap-typeahead';
import {Form, OverlayTrigger, Tooltip} from 'react-bootstrap';
import {
  ChevronDown,
  ChevronUp,
  X,
  ExclamationCircle,
} from 'react-bootstrap-icons';
import CheckboxComponent from './CheckboxComponent';

export type Option = string | Record<string, any>;
export type DropdownTypeaheadProps = {
  label: string;
  options: Option[];
  selected: Option | Option[] | null | undefined;
  onChange: (value: Option | Option[] | null) => void;
  onInputChange?: (query: string) => void;
  onBlur?: () => void;
  multiple?: boolean;
  required?: boolean;
  disabled?: boolean;
  isInvalid?: boolean;
  errorMessage?: string;
  hideLabel?: boolean;
  disabledSelectPrefix?: boolean;
  specialOptionLabel?: string;
  onSpecialOptionSelect?: () => void;
  useCheckboxes?: boolean;
};

const DropdownTypeahead: React.FC<DropdownTypeaheadProps> = ({
  label,
  options,
  selected,
  onChange,
  onInputChange,
  onBlur,
  hideLabel = false,
  required = false,
  disabled = false,
  isInvalid = false,
  errorMessage = 'This field is required',
  multiple = false,
  disabledSelectPrefix = false,
  specialOptionLabel,
  onSpecialOptionSelect,
  useCheckboxes = false,
}) => {
  const [isMenuOpen, setIsMenuOpen] = React.useState(false);
  const [userInteracted, setUserInteracted] = React.useState(false);
  const typeaheadRef = useRef<any>(null);
  const hasSelectedValue = () => {
    if (!selected) return false;
    return Array.isArray(selected) ? selected.length > 0 : true;
  };

  let selectedValue: Option[] = [];

  if (selected) {
    if (Array.isArray(selected)) {
      selectedValue = selected;
    } else {
      selectedValue = [selected];
    }
  }

  const renderActionIcon = () => {
    if (isInvalid && !hasSelectedValue() && multiple) {
      return (
        <ExclamationCircle
          data-testid="danger-icon"
          size={14}
          color="#dc3545"
        />
      );
    }
    if (isInvalid) return;
    if (hasSelectedValue()) {
      return <X data-testid="cross-icon" size={18} />;
    }
    return isMenuOpen ? <ChevronUp size={14} /> : <ChevronDown size={14} />;
  };

  return (
    <Form.Group>
      <Form.Label style={{display: hideLabel ? 'none' : 'block'}}>
        {label}
        {required && '*'}
      </Form.Label>
      <div
        role="button"
        tabIndex={0}
        onClick={() => setUserInteracted(true)}
        onKeyDown={e => {
          if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            setUserInteracted(true);
          }
        }}
        className={`typeahead-wrapper ${isInvalid ? 'is-invalid' : ''} ${
          multiple ? 'multiple-selection' : ''
        }`}
        style={{
          all: 'unset',
          position: 'relative',
          width: '100%',
          ...(isInvalid && {
            border: '1px solid #dc3545',
            borderRadius: '4px',
            ...(multiple && {
              backgroundColor: '#fff',
            }),
          }),
        }}
      >
        <Typeahead
          ref={typeaheadRef}
          id={`${label.replace(/\s+/g, '-').toLowerCase()}-typeahead`}
          labelKey="label"
          maxResults={250}
          options={options}
          onMenuToggle={isOpen => {
            setIsMenuOpen(isOpen);
            if (!isOpen && userInteracted && onBlur) {
              onBlur();
              setUserInteracted(false);
            }
          }}
          placeholder={disabledSelectPrefix ? label : `Select ${label}`}
          onChange={(selectedArr: Option[]) => {
            // When using checkboxes, we handle selection manually
            if (useCheckboxes && multiple) {
              return;
            }

            if (multiple) {
              onChange(selectedArr.length > 0 ? selectedArr : null);
            } else {
              onChange(selectedArr[0] || null);
            }
          }}
          onInputChange={onInputChange}
          selected={selectedValue}
          minLength={0}
          disabled={disabled}
          multiple={multiple}
          filterBy={useCheckboxes && multiple ? () => true : undefined}
          inputProps={{
            className: `fs-14 ${isInvalid ? 'is-invalid' : ''}`,
            style: {
              paddingRight: '30px',
              ...(isInvalid && {
                borderColor: '#dc3545',
                boxShadow: '0 !important',
              }),
            },
            onFocus: () => {
              setUserInteracted(true);
            },
            onKeyDown: () => {
              setUserInteracted(true);
            },
          }}
          renderMenu={(optionList: Option[], menuProps) => {
            // When using checkboxes, always show all options
            const displayOptions = useCheckboxes && multiple ? options : optionList;
            const getOptionKey = (option: Option, index: number) => {
              if (typeof option === 'object') {
                return option.id ?? option.value ?? option.label ?? index;
              }
              return option;
            };

            const getOptionLabel = (option: Option) => {
              return typeof option === 'object' && 'label' in option
                ? option.label
                : option;
            };

            const isOptionSelected = (option: Option) => {
              if (!selected) return false;
              const selectedArray = Array.isArray(selected) ? selected : [selected];
              return selectedArray.some(selectedOption => {
                if (typeof option === 'object' && typeof selectedOption === 'object') {
                  return (option.id && selectedOption.id && option.id === selectedOption.id) ||
                         (option.value && selectedOption.value && option.value === selectedOption.value) ||
                         (option.label && selectedOption.label && option.label === selectedOption.label);
                }
                return option === selectedOption;
              });
            };

            const handleCheckboxChange = (option: Option) => {
              if (!multiple) return;

              const selectedArray = Array.isArray(selected) ? selected : (selected ? [selected] : []);
              const isSelected = isOptionSelected(option);

              let newSelection: Option[];
              if (isSelected) {
                // Remove the option
                newSelection = selectedArray.filter(selectedOption => {
                  if (typeof option === 'object' && typeof selectedOption === 'object') {
                    return !((option.id && selectedOption.id && option.id === selectedOption.id) ||
                            (option.value && selectedOption.value && option.value === selectedOption.value) ||
                            (option.label && selectedOption.label && option.label === selectedOption.label));
                  }
                  return option !== selectedOption;
                });
              } else {
                // Add the option
                newSelection = [...selectedArray, option];
              }

              onChange(newSelection.length > 0 ? newSelection : null);
            };

            const renderOptions = () =>
              displayOptions.map((option: Option, i: number) => {
                if (useCheckboxes && multiple) {
                  return (
                    <div
                      key={getOptionKey(option, i)}
                      className="dropdown-item-checkbox"
                      style={{
                        padding: '8px 12px',
                        cursor: 'pointer',
                      }}
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        handleCheckboxChange(option);
                      }}
                    >
                      <CheckboxComponent
                        id={`checkbox-${getOptionKey(option, i)}`}
                        checked={isOptionSelected(option)}
                        onChange={() => handleCheckboxChange(option)}
                        label={
                          <div
                            className="typehead-option-tooltip"
                            title={getOptionLabel(option)}
                          >
                            {getOptionLabel(option)}
                          </div>
                        }
                      />
                    </div>
                  );
                }

                return (
                  <MenuItem
                    key={getOptionKey(option, i)}
                    option={option}
                    position={i}
                  >
                    <div
                      className="typehead-option-tooltip"
                      title={getOptionLabel(option)}
                    >
                      {getOptionLabel(option)}
                    </div>
                  </MenuItem>
                );
              });

            const renderSpecialOption = () =>
              specialOptionLabel && onSpecialOptionSelect ? (
                <button
                  type="button"
                  className="dropdown-special-item"
                  onClick={onSpecialOptionSelect}
                >
                  {specialOptionLabel}
                </button>
              ) : null;

            return (
              <div
                className="rbt-menu dropdown-menu show nbp-typehead-dropdown"
                {...menuProps}
              >
                {displayOptions.length === 0 ? (
                  <div className="dropdown-no-results">No results found</div>
                ) : (
                  renderOptions()
                )}
                {renderSpecialOption()}
              </div>
            );
          }}
          renderToken={(_option, _props, index) => {
            const visibleCount = 4;

            // Only render on the first token
            if (index !== 0) return <></>;

            const selectedValues = Array.isArray(selected)
              ? selected
              : [selected];

            const visibleItems = selectedValues.slice(0, visibleCount);
            const hiddenItems = selectedValues.slice(visibleCount);

            const visibleLabels = visibleItems.map(opt => opt.label).join(', ');
            const hiddenLabels = hiddenItems.map(opt => opt.label).join(', ');

            return (
              <div className="rbt-token">
                <span>{visibleLabels}</span>
                {hiddenItems.length > 0 && (
                  <OverlayTrigger
                    placement="top"
                    overlay={
                      <Tooltip id="info-tooltip">{hiddenLabels}</Tooltip>
                    }
                  >
                    <span className="rbt-token-more">
                      +{hiddenItems.length} More
                    </span>
                  </OverlayTrigger>
                )}
              </div>
            );
          }}
        />
        {!disabled && (
          <div
            className="clear-icon"
            data-testid="clear-icon"
            style={{
              position: 'absolute',
              right: '10px',
              top: '50%',
              transform: 'translateY(-50%)',
              zIndex: 4,
            }}
          >
            <button
              type="button"
              style={{
                all: 'unset', // Removes all default styles
              }}
              className="cursor-pointer"
              onClick={() => {
                if (hasSelectedValue()) {
                  onChange(null);
                } else if (!isInvalid) {
                  typeaheadRef.current?.toggleMenu?.(); // Opens the dropdown
                }
              }}
            >
              {renderActionIcon()}
            </button>
          </div>
        )}
      </div>
      {isInvalid && (
        <div className="invalid-feedback" style={{display: 'block'}}>
          {errorMessage}
        </div>
      )}
    </Form.Group>
  );
};

export default DropdownTypeahead;
