import classNames from 'classnames';
import React from 'react';

interface IProps {
  isOverlayLoader?: boolean;
  className?: string;
}

const Loader = (props: IProps) => {
  const {className, isOverlayLoader} = props;

  return (
    <div
      className={classNames(
        'w-full h-full flex-1 d-flex justify-content-center align-items-center',
        className,
        {'overlay-loader': isOverlayLoader},
      )}
    >
      <div
        className="spinner-border border-5 text-secondary"
        data-testid="defaultLoader"
      ></div>
    </div>
  );
};

export default Loader;
