import React, {useState, useEffect, useRef} from 'react';
import {OverlayTrigger, Popover} from 'react-bootstrap';
import '../styles/components/badge.scss';

interface BadgeProps {
  label: string;
}

const Badge: React.FC<BadgeProps> = ({label}) => {
  const badgeRef = useRef<HTMLDivElement>(null);
  const [badgeWidth, setBadgeWidth] = useState<number | null>(null);

  useEffect(() => {
    const updateBadgeWidth = () => {
      if (badgeRef.current) {
        setBadgeWidth(badgeRef.current.offsetWidth);
      }
    };

    const resizeObserver = new ResizeObserver(updateBadgeWidth);
    if (badgeRef.current) {
      resizeObserver.observe(badgeRef.current);
    }

    updateBadgeWidth(); // Initial calculation

    return () => {
      resizeObserver.disconnect();
    };
  }, []);

  return (
    <div
      ref={badgeRef}
      className="ra-badge"
      style={{width: badgeWidth ? `${badgeWidth}px` : 'auto'}}
    >
      {label}
    </div>
  );
};

interface BadgeListProps {
  badges: string[];
}

const BadgeListPopover: React.FC<BadgeListProps> = ({badges}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [visibleCount, setVisibleCount] = useState(badges.length);

  useEffect(() => {
    const updateVisibleCount = () => {
      const container = containerRef.current;
      if (container) {
        const containerWidth = container.offsetWidth;

        // Calculate the width of all badges including the remaining count badge
        const badgeWidths = Array.from(container.children).map(
          child => (child as HTMLDivElement).offsetWidth,
        );

        const remainingBadgeWidth = 80; // Approximate width of "+X more" badge
        const totalBadgeWidth =
          badgeWidths.reduce((acc, width) => acc + width, 0) +
          remainingBadgeWidth;

        // Calculate the maximum number of visible badges
        const maxVisibleBadges = Math.floor(
          containerWidth / (totalBadgeWidth / badgeWidths.length),
        );
        setVisibleCount(maxVisibleBadges);
      }
    };

    const resizeObserver = new ResizeObserver(updateVisibleCount);
    if (containerRef.current) {
      resizeObserver.observe(containerRef.current);
    }

    updateVisibleCount(); // Initial calculation

    return () => {
      resizeObserver.disconnect();
    };
  }, [badges]);

  const visibleBadges = badges.slice(0, visibleCount);
  const remainingBadges = badges.slice(visibleCount);
  const remainingCount = badges.length - visibleCount;

  const popover = (
    <Popover id="badge-list-popover" className="ra-single-badge-popover">
      <Popover.Body className="ra-single-badge-popover-body">
        {remainingBadges.join(', ')}
      </Popover.Body>
    </Popover>
  );

  return (
    <div className="ra-badge-list" ref={containerRef}>
      {visibleBadges.map(badge => (
        <Badge key={`${badge}`} label={badge} />
      ))}
      {remainingCount > 0 && (
        <OverlayTrigger
          trigger={['hover', 'focus']}
          placement="top"
          overlay={popover}
        >
          <div className="ra-badge">+{remainingCount} more</div>
        </OverlayTrigger>
      )}
    </div>
  );
};

export default BadgeListPopover;
