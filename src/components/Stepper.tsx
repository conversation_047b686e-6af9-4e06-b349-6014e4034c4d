import React from 'react';
import {Card, Row, Col} from 'react-bootstrap';
import '../styles/styles.scss';
import {RoundCheckFilled} from '../utils/svgIcons';

type StepperProps = {
  steps: string[];
  currentStep: number;
  setStep: (step: number) => void;
};

const Stepper: React.FC<StepperProps> = ({steps, currentStep, setStep}) => {
  return (
    <div className="d-flex flex-column" style={{gap: '1rem'}}>
      {steps.map((label, index) => {
        const stepNumber = index + 1;
        const isCompleted = stepNumber < currentStep;
        const isActive = stepNumber === currentStep;

        return (
          <Card
            key={label}
            onClick={() => {
              if (isCompleted) setStep(stepNumber);
            }}
            style={{
              border: isActive ? '2px solid #A6CBF3' : '1px solid #CCCCCC',
              boxShadow: isActive
                ? '0 0 0 1px rgba(59, 130, 246, 0.2)'
                : 'none',
              borderRadius: '6px',
              padding: '16px 12px',
              gap: '8px',
              cursor: isCompleted ? 'pointer' : 'default',
              opacity: isCompleted || isActive ? 1 : 0.6,
            }}
          >
            <Row className="align-items-center justify-content-between">
              <Col>
                <div className="fs-14 fw-500 secondary-color">
                  Step {stepNumber}
                </div>
                <div className="stepper-lbl fs-16 fw-600 primary-txt-color">
                  {label}
                </div>
              </Col>
              <Col xs="auto">
                {isCompleted ? (
                  <RoundCheckFilled />
                ) : (
                  <div
                    style={{
                      width: 24,
                      height: 24,
                      borderRadius: '50%',
                      border: '1px solid #CCCCCC',
                    }}
                  ></div>
                )}
              </Col>
            </Row>
          </Card>
        );
      })}
    </div>
  );
};

export default Stepper;
