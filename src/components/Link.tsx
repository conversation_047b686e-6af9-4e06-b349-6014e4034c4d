import React from 'react';
import {useNavigate} from 'react-router-dom';
import classNames from 'classnames';

import '../styles/components/link.scss';

interface LinkProps {
  href: string;
  children: React.ReactNode;
  className?: string;
}

const Link: React.FC<LinkProps> = props => {
  const navigate = useNavigate();

  return (
    <a
      href={props.href}
      className={classNames(props.className, 'ra-link')}
      onClick={e => {
        e.preventDefault();
        navigate(props.href);
      }}
    >
      {props.children}
    </a>
  );
};

export default Link;
