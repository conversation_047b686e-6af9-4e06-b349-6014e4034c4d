import React from 'react';
import {Form, InputGroup} from 'react-bootstrap';

export interface UnitSelectInputComponentProps {
  onChange: any;
  unitFeildName: string; // Default unit field name
  unitOptions: string[]; // Default unit options
  placeholder?: string;
  className?: string;
  disabled?: boolean;
  maxLength?: number;
  form: {[key: string]: any};
  classes?: any;
  label: string;
  name: string; // Add this line to define the key prop aka form feild key name
  formControlId?: string; // Optional prop for Form.Control's controlId
  helpText?: string; // Optional prop for additional help text
  error?: string; // Optional prop for validation text
}

export const UnitSelectInputComponent: React.FC<UnitSelectInputComponentProps> =
  ({
    form,
    label,
    onChange,
    placeholder = '',
    className = '',
    disabled = false,
    name = '',
    maxLength = null,
    classes,
    formControlId = null,
    helpText = '',
    error = '',
    unitFeildName = '', // Default unit field name
    unitOptions = [], // Default unit options
  }) => {
    return (
      <Form.Group
        className={classes?.group ?? 'mb-3'}
        controlId={formControlId ?? name}
      >
        <Form.Label className={classes?.label}>{label}</Form.Label>
        <InputGroup>
          <Form.Control
            placeholder={placeholder}
            className={`${classes?.component ?? ''} ${className}`}
            disabled={disabled}
            name={name}
            type="text"
            inputMode="numeric"
            pattern="\d*"
            value={form?.[name] ?? ''}
            onChange={onChange}
            isInvalid={!!error}
          />
          <Form.Select
            value={form?.[unitFeildName] ?? ''}
            onChange={onChange}
            name={unitFeildName}
            data-testid="deadweight-unit-select"
            className={`fs-14 ${error ? 'is-invalid' : ''}`}
          >
            {unitOptions.map(unit => (
              <option key={unit} value={unit}>
                {unit}
              </option>
            ))}
          </Form.Select>
        </InputGroup>
        <div style={{display: 'flex', justifyContent: 'space-between'}}>
          {helpText && <Form.Text muted>{helpText}</Form.Text>}
          {maxLength && (
            <Form.Text muted>
              {form?.[name]?.length ?? 0}/{maxLength}
            </Form.Text>
          )}
        </div>
        {error && (
          <Form.Control.Feedback type="invalid">{error}</Form.Control.Feedback>
        )}
      </Form.Group>
    );
  };
