import React from 'react';
import {OverlayTrigger, Popover} from 'react-bootstrap';
import '../styles/components/single-badge-popover.scss';

interface SingleBadgePopoverProps {
  items: string[];
  label: string;
  placement?: 'top' | 'right' | 'bottom' | 'left';
}

const SingleBadgePopover: React.FC<SingleBadgePopoverProps> = ({
  items,
  label: title,
  placement = 'top',
}) => {
  const popover = (
    <Popover id="single-badge-popover" className="ra-single-badge-popover">
      <Popover.Body className="ra-single-badge-popover-body">
        {items.join(', ')}
      </Popover.Body>
    </Popover>
  );

  return (
    <OverlayTrigger
      trigger={['hover', 'focus']}
      placement={placement}
      overlay={popover}
    >
      <div className="ra-single-badge-popover-container">
        <span className="ra-single-badge-popover-button">{title}</span>
      </div>
    </OverlayTrigger>
  );
};

export default SingleBadgePopover;
