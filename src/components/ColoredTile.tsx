import React from 'react';
import classNames from 'classnames';

import '../styles/components/colored-tile.scss';

export type ColoredTileTheme = 'red' | 'green' | 'yellow' | 'blue';

export interface ColoredTileProps {
  text: string;
  theme: ColoredTileTheme;
  className?: string;
}

const ColoredTile: React.FC<ColoredTileProps> = ({text, theme, className}) => {
  return (
    <span
      className={classNames(`colored-tile colored-tile-${theme}`, className)}
    >
      {text}
    </span>
  );
};

export default ColoredTile;
