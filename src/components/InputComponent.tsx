import React from 'react';
import {Form} from 'react-bootstrap';

export interface InputComponentProps {
  value: string;
  onChange: any;
  onBlur?: any;
  placeholder?: string;
  rows?: number;
  className?: string;
  disabled?: boolean;
  maxLength?: number;
  style?: React.CSSProperties;
  form?: {[key: string]: any};
  classes?: any;
  label: string;
  name: string;
  type?: 'text' | 'number' | 'email' | 'password' | 'textarea';
  formControlId?: string;
  helpText?: string;
  error?: string;
  showMaxLength?: boolean; // This prop is not used in the current implementation but can be used for future enhancements
}

export const InputComponent: React.FC<InputComponentProps> = ({
  form,
  label = '',
  onChange,
  onBlur,
  placeholder = '',
  rows = 3,
  className = '',
  disabled = false,
  style = {},
  name = '',
  maxLength = 255,
  showMaxLength = false,
  classes,
  type = 'text',
  formControlId = null,
  helpText = '',
  error = '',
  value,
}) => {
  return (
    <Form.Group
      className={classes?.group ?? 'mb-3'}
      controlId={formControlId ?? name}
    >
      <Form.Label className={classes?.label}>{label}</Form.Label>
      <Form.Control
        as={type === 'textarea' ? 'textarea' : 'input'}
        placeholder={placeholder}
        className={`${classes?.component ?? ''} ${className}`}
        style={style}
        disabled={disabled}
        name={name}
        value={value ?? form?.[name] ?? ''}
        {...(type === 'textarea' ? {rows} : {})}
        onChange={onChange}
        onBlur={onBlur}
        maxLength={maxLength ?? undefined}
        isInvalid={!!error}
        autoComplete="off"
      />
      <div style={{display: 'flex', justifyContent: 'space-between'}}>
        <div>
          {<Form.Text muted>{helpText ?? ''}</Form.Text>}
          {<Form.Text style={{color: '#dc3545'}}>{error ?? ''}</Form.Text>}
        </div>
        {showMaxLength && (
          <Form.Text muted>
            {form?.[name]?.length ?? 0}/{maxLength}
          </Form.Text>
        )}
      </div>
    </Form.Group>
  );
};
