import React from 'react';
import {Link} from 'react-router-dom';

type BreadCrumbItem = {
  title: string;
  link?: string;
  state?: any;
};

export type ProjectBreadCrumbProps = {
  items: BreadCrumbItem[];
  options?: React.ReactElement;
};

const ProjectBreadCrumb: React.FC<ProjectBreadCrumbProps> = ({
  items,
  options,
}) => {
  return (
    <div className="d-flex justify-content-between align-items-center w-100">
      <div className="fs-20" style={{color: '#1F4A70', fontWeight: 600}}>
        {items.map((item, idx) => (
          <span key={`link-${item.title}`}>
            {item.link ? (
              <Link
                className="underline fs-20"
                style={{
                  color: '#1F4A70',
                  textDecoration: 'underline',
                  fontSize: '20px',
                }}
                to={item.link}
                state={item.state}
              >
                {item.title}
              </Link>
            ) : (
              <span className="fs-20">{item.title}</span>
            )}
            {idx < items.length - 1 && ' / '}
          </span>
        ))}
      </div>
      {options || null}
    </div>
  );
};

export default ProjectBreadCrumb;
