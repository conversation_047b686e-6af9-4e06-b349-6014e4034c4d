import React, {useState, useEffect} from 'react';
import {Col, Form, Row} from 'react-bootstrap';
import {TemplateForm} from '../types/template';
import {RiskForm} from '../types/risk';
import DropdownTypeahead from './DropdownTypeahead';
import SingleVesselOfficeDropdown from './SingleVesselOfficeDropdown';
import CustomDatePicker from './CustomDatePicker';
import {assessorOptions} from '../pages/CreateRA/BasicDetails';
import {
  formatDateToYYYYMMDD,
  createGroupedVesselOfficeOptions,
  findSelectedVesselOfficeOption,
  SingleVesselOfficeOption,
  GroupedVesselOfficeOption,
} from '../utils/helper';
import {useDataStoreContext} from '../context';
import {vesselStatusAndLabelName} from '../utils/common';
import SearchDropdown from './SearchDropdown';
import {raLevels} from '../pages/RAListing/components/RAFilters';
import {RaLevel} from '../enums';
import FormCheckRadio from './FormCheckRadio';
import {RACategoryRiskFormOption} from '../constants/template';
import {
  getVesselsList,
  getOfficesList,
  getApprovalsRequiredList,
} from '../services/services';
import Loader from './Loader';

const EditBasicDetailsComp = ({
  clonedForm,
  setClonedForm,
  type = 'template',
}: {
  clonedForm: TemplateForm | RiskForm;
  setClonedForm: React.Dispatch<React.SetStateAction<TemplateForm | RiskForm>>;
  type?: 'template' | 'risk';
}) => {
  const {
    roleConfig: {
      riskAssessment: {canAddRALvl1},
      user,
    },
    dataStore: {
      vesselListForRisk,
      officeListForRisk,
      approversReqListForRiskOffice,
      approversReqListForRiskVessel,
    },
  } = useDataStoreContext();
  const errorMsg = 'This is a mandatory field. Please fill to process.';
  const [approvalOptionsOffice, setApprovalOptionsOffice] = useState<
    {value: number; label: string}[]
  >([]);
  const [approvalOptionsVessel, setApprovalOptionsVessel] = useState<
    {value: number; label: string}[]
  >([]);
  const [groupedVesselOfficeOptions, setGroupedVesselOfficeOptions] = useState<
    GroupedVesselOfficeOption[]
  >([]);
  const [displayApprovalDate, setDisplayApprovalDate] = useState(
    clonedForm.ra_level === RaLevel.ROUTINE,
  );
  const [loading, setLoading] = useState(false);

  // Load vessel and office options for risk forms
  useEffect(() => {
    if (type === 'risk') {
      const loadOptions = async () => {
        try {
          setLoading(true);
          // Create grouped options using helper function
          const vesselList = vesselListForRisk.length
            ? vesselListForRisk
            : await getVesselsList();
          const officeList = officeListForRisk.length
            ? officeListForRisk
            : await getOfficesList();
          const groupedOptions = createGroupedVesselOfficeOptions(
            vesselList,
            officeList,
            vesselStatusAndLabelName,
          );
          setGroupedVesselOfficeOptions(groupedOptions);

          const approversReqListForRiskOfficeLoc =
            approversReqListForRiskOffice.length
              ? approversReqListForRiskOffice
              : await getApprovalsRequiredList(1);
          const approversReqListForRiskVesselLoc =
            approversReqListForRiskVessel.length
              ? approversReqListForRiskVessel
              : await getApprovalsRequiredList(2);

          setApprovalOptionsOffice(
            approversReqListForRiskOfficeLoc.map(item => ({
              value: item.id,
              label: item.name,
            })),
          );

          setApprovalOptionsVessel(
            approversReqListForRiskVesselLoc.map(item => ({
              value: item.id,
              label: item.name,
            })),
          );
        } catch (error) {
          console.error('Error loading vessel/office options:', error);
        } finally {
          setLoading(false);
        }
      };

      loadOptions();
    }
  }, [
    type,
    vesselListForRisk,
    officeListForRisk,
    approversReqListForRiskOffice,
    approversReqListForRiskVessel,
  ]);

  // Handle dropdown changes for risk form
  const handleDropdownChange = (name: string, value: any) => {
    setClonedForm({
      ...clonedForm,
      [name]: value,
    });
  };

  // Handle vessel/office dropdown change
  const handleVesselOfficeChange = (
    selected: SingleVesselOfficeOption | null,
  ) => {
    const value = selected?.value ? Number(selected.value) : 0;
    const vesselId = selected?.vesselId;

    // Update both vessel_ownership_id and vessel_id for risk forms
    setClonedForm(prev => ({
      ...prev,
      ...(vesselId
        ? {
            vessel_ownership_id: value,
            vessel_id: vesselId,
            office_id: 0,
            office_name: '',
          }
        : {
            office_id: value,
            office_name: selected?.label ?? '',
            vessel_ownership_id: 0,
            vessel_id: 0,
          }),
    }));
  };

  // Handle approval required dropdown change
  const handleApprovalChange = (selected: any) => {
    let values: number[] = [];

    if (Array.isArray(selected)) {
      values = selected.map((item: any) => item.value);
    } else if (selected) {
      values = [selected.value];
    }

    setClonedForm({
      ...clonedForm,
      approval_required: values,
    } as RiskForm);
  };

  // Handle date changes for risk form
  const handleDateChange = (name: string, date?: Date) => {
    setClonedForm({
      ...clonedForm,
      [name]: date ? formatDateToYYYYMMDD(date) : '',
    });
  };

  const canUserUpdateRaLevel = (clonedForm as RiskForm)?.risk_approver
    ?.filter(approver => approver.approval_status === null)
    .map(approver => approver.keycloak_id)
    .includes(user.user_id);

  return (
    <>
      {loading && <Loader isOverlayLoader />}
      <Row className="mb-3">
        <Col>
          <Form.Group controlId="taskRequiringRa">
            <Form.Label className="fs-14 fw-500">
              Task Requiring R.A.
            </Form.Label>
            <Form.Control
              type="text"
              value={clonedForm.task_requiring_ra}
              onChange={e => {
                setClonedForm({
                  ...clonedForm,
                  task_requiring_ra: e.target.value,
                });
              }}
              maxLength={255}
              className="fs-14"
              isInvalid={clonedForm.task_requiring_ra.trim() === ''}
            />
            <Form.Control.Feedback type="invalid">
              {errorMsg}
            </Form.Control.Feedback>
          </Form.Group>
        </Col>
      </Row>

      {/* Risk Form specific fields */}
      {type === 'risk' && (
        <>
          <Row className="mb-3">
            <Col md={6}>
              <DropdownTypeahead
                label="Assessor"
                options={assessorOptions}
                selected={assessorOptions.filter(
                  opt => opt.value === (clonedForm as RiskForm).assessor,
                )}
                onChange={(selected: any) => {
                  const value = Array.isArray(selected)
                    ? selected[0]?.value
                    : selected?.value;
                  handleDropdownChange('assessor', value);
                }}
                isInvalid={!(clonedForm as RiskForm).assessor}
                errorMessage={errorMsg}
              />
            </Col>
            <Col md={6}>
              <SingleVesselOfficeDropdown
                label="Vessel/Office"
                options={groupedVesselOfficeOptions}
                value={findSelectedVesselOfficeOption(
                  groupedVesselOfficeOptions,
                  (clonedForm as RiskForm).office_id
                    ? (clonedForm as RiskForm).office_id
                    : (clonedForm as RiskForm).vessel_ownership_id,
                )}
                onChange={handleVesselOfficeChange}
                isInvalid={
                  !(clonedForm as RiskForm).vessel_ownership_id &&
                  !(clonedForm as RiskForm).office_id
                }
                errorMessage={errorMsg}
                placeholder="Select Vessel/Office"
              />
            </Col>
          </Row>
          <Row className="mb-3">
            <Col md={6}>
              <CustomDatePicker
                label="Date of Risk Assessment"
                value={
                  (clonedForm as RiskForm).date_risk_assessment
                    ? new Date((clonedForm as RiskForm).date_risk_assessment)
                    : undefined
                }
                onChange={date =>
                  handleDateChange('date_risk_assessment', date)
                }
                placeholder="Select Date"
                controlId="date_risk_assessment"
                isRequired={true}
                errorMsg={
                  !(clonedForm as RiskForm).date_risk_assessment ? errorMsg : ''
                }
                minDate={undefined}
              />
            </Col>
            <Col md={6}>
              <DropdownTypeahead
                label="Approvals Required (if necessary)"
                options={
                  (clonedForm as RiskForm).assessor === 2
                    ? approvalOptionsVessel
                    : approvalOptionsOffice
                }
                selected={
                  (clonedForm as RiskForm).assessor === 2
                    ? approvalOptionsVessel.filter(opt =>
                        (clonedForm as RiskForm).approval_required?.includes(
                          opt.value,
                        ),
                      )
                    : approvalOptionsOffice.filter(opt =>
                        (clonedForm as RiskForm).approval_required?.includes(
                          opt.value,
                        ),
                      )
                }
                onChange={handleApprovalChange}
                multiple={true}
                isInvalid={
                  !(clonedForm as RiskForm).approval_required ||
                  (clonedForm as RiskForm).approval_required.length === 0
                }
                errorMessage={errorMsg}
                useCheckboxes
              />
            </Col>
          </Row>

          {canUserUpdateRaLevel && (
            <Row className="mb-3">
              <Col md={6}>
                <Form.Group
                  className="mb-3"
                  controlId="approver-search-dropdown-Level-of-RA"
                >
                  <Form.Label id="approver-search-dropdown-Level-of-RA">
                    Level of RA
                  </Form.Label>

                  <SearchDropdown
                    placeholder="Set Level of R.A."
                    options={raLevels.filter(
                      item => item.value !== RaLevel.LEVEL_1_RA,
                    )}
                    canClear={false}
                    selected={
                      (clonedForm as RiskForm).ra_level
                        ? [
                            (clonedForm as RiskForm)
                              .ra_level as unknown as RaLevel,
                          ]
                        : null
                    }
                    onChange={value => {
                      const ra = value[0];
                      if (ra) {
                        setClonedForm(prev => ({
                          ...prev,
                          ra_level: Number(ra),
                        }));
                      }

                      // Show approval date for Routine RA
                      if (ra === RaLevel.ROUTINE) setDisplayApprovalDate(true);
                      else setDisplayApprovalDate(false); // Hide approval date if RA level is not Routine
                    }}
                    multiple={false}
                  />
                </Form.Group>
              </Col>
              <Col md={6}>
                {displayApprovalDate && (
                  <CustomDatePicker
                    label="Date of Approval"
                    value={
                      (clonedForm as RiskForm).approval_date
                        ? new Date(
                            String((clonedForm as RiskForm).approval_date),
                          )
                        : undefined
                    }
                    onChange={date => handleDateChange('approval_date', date)}
                    placeholder="Select Date"
                    controlId="approval_date"
                    isRequired={true}
                    errorMsg={
                      !(clonedForm as RiskForm).approval_date ? errorMsg : ''
                    }
                    minDate={undefined}
                  />
                )}
              </Col>
            </Row>
          )}
        </>
      )}
      <Row className="mb-3">
        <Col md={6}>
          <Form.Group controlId="taskDuration">
            <Form.Label className="fs-14 fw-500">Task Duration</Form.Label>
            <Form.Control
              type="text"
              value={clonedForm.task_duration}
              onChange={e => {
                setClonedForm({
                  ...clonedForm,
                  task_duration: e.target.value,
                });
              }}
              maxLength={255}
              placeholder="Enter No. of Days Required"
              isInvalid={clonedForm.task_duration.trim() === ''}
            />
            <Form.Control.Feedback type="invalid">
              {errorMsg}
            </Form.Control.Feedback>
          </Form.Group>
          <Form.Text className="text-muted fs-14">
            Mention if values are in Days/Hours
          </Form.Text>
        </Col>
        <Col md={6}>
          {type === 'risk' && canAddRALvl1 && (
            <Form.Group className="mb-3" key="ra-category">
              <Form.Label
                style={{fontWeight: 400, fontSize: '16px', marginBottom: 8}}
              >
                RA Category
              </Form.Label>
              <div className="d-flex align-items-center" style={{gap: 16}}>
                {RACategoryRiskFormOption.map(({label, value}) => (
                  <FormCheckRadio
                    key={label}
                    checked={clonedForm?.ra_level === value}
                    onChange={() =>
                      setClonedForm((prev: any) => ({
                        ...prev,
                        ra_level: value,
                      }))
                    }
                    label={label}
                    name="ra_category"
                    value={value}
                    className="me-2 fs-14 fw-400"
                    id={`ra_category-select-${value}`}
                    disabled={false}
                  />
                ))}
              </div>
            </Form.Group>
          )}
        </Col>
      </Row>
    </>
  );
};

export default EditBasicDetailsComp;
