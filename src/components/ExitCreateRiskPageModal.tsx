import React from 'react';
import {Mo<PERSON>, But<PERSON>} from 'react-bootstrap';

type Props = {
  onClose: () => void;
  handleSubmit: () => void;
};

export const ExitCreateRiskPageModal: React.FC<Props> = ({
  onClose,
  handleSubmit,
}) => {
  return (
    <Modal
      show
      onHide={onClose}
      size="lg"
      backdrop="static"
      dialogClassName="top-modal"
    >
      <Modal.Header>
        <Modal.Title>Exit RA Creation without Saving</Modal.Title>
      </Modal.Header>
      <Modal.Body className="complete-project-modal">
        <div
          className="alert d-flex align-items-center fs-14 ra-alert-warning"
          role="alert"
          data-testid="error-alert"
        >
          <div>
            <strong>
              Are you sure you want to exit without saving this Risk Assessment
              as a draft?
            </strong>
            <span> All entered information will be lost permanently.</span>
          </div>
        </div>
      </Modal.Body>

      <Modal.Footer>
        <Button variant="primary" className="me-2 fs-14" onClick={handleSubmit}>
          Discard RA
        </Button>
        <Button
          variant="secondary"
          className="me-2 fs-14"
          onClick={() => onClose()}
        >
          Keep Editing
        </Button>
      </Modal.Footer>
    </Modal>
  );
};
