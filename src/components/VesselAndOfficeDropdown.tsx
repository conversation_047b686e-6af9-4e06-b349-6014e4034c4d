import React, {
  useState,
  useMemo,
  useRef,
  useEffect,
  useLayoutEffect,
} from 'react';
import {ChevronDown} from 'react-bootstrap-icons';
import SearchInput from './SearchInput';
import CheckboxComponent from './CheckboxComponent';
import SingleBadgePopover from './SingleBadgePopover';
import {RAFilterValues} from '../pages/RAListing/components/RAFilters';

import '../styles/components/vessel-and-office-dropdown.scss';
import classNames from 'classnames';

interface GroupedOption {
  label: string;
  data: Array<{
    id: number | string;
    name: string;
  }>;
}

interface VesselAndOfficeDropdowItemProps {
  options: GroupedOption[];
  value: RAFilterValues['vessel_or_office'];
  onChange: (selected: RAFilterValues['vessel_or_office']) => void;
  onClose: () => void;
}

const VesselAndOfficeDropdownItem: React.FC<VesselAndOfficeDropdowItemProps> =
  ({options = [], value, onChange, onClose}) => {
    const [search, setSearch] = useState<string>('');

    const filteredGroups = useMemo(() => {
      if (!search) {
        return options;
      }
      return options
        .map(group => ({
          ...group,
          data: group.data.filter(option =>
            option.name.toLowerCase().includes(search.toLowerCase()),
          ),
        }))
        .filter(group => group.data.length > 0);
    }, [options, search]);

    const handleOptionSelection = (
      groupLabel: string,
      option: {id: number | string; name: string},
    ): void => {
      const isOffice = groupLabel === 'Offices';
      const optionId = Number(option.id);

      const getUpdatedIds = (
        currentIds: (number | null)[] | null,
        idToToggle: number,
      ): number[] | null => {
        if (!currentIds) return [idToToggle];
        const exists = currentIds.includes(idToToggle);
        const updated = exists
          ? currentIds.filter(id => id !== idToToggle)
          : [...currentIds, idToToggle];
        const filtered = updated.filter((id): id is number => id !== null);
        return filtered.length > 0 ? filtered : null;
      };

      const newValue: RAFilterValues['vessel_or_office'] = {
        vessel_id: value?.vessel_id ?? null,
        office_id: value?.office_id ?? null,
      };

      if (isOffice) {
        newValue.office_id = getUpdatedIds(newValue.office_id, optionId);
      } else {
        newValue.vessel_id = getUpdatedIds(newValue.vessel_id, optionId);
      }

      onChange(newValue);
    };

    const handleSelectAll = (): void => {
      if (filteredGroups.length === 0) return;

      const hasSelectedItems =
        (value?.vessel_id?.length ?? 0) > 0 ||
        (value?.office_id?.length ?? 0) > 0;

      if (hasSelectedItems) {
        // Clear all selections
        onChange({
          vessel_id: null,
          office_id: null,
        });
      } else {
        // Select all available vessels and offices
        const newValue: RAFilterValues['vessel_or_office'] = {
          vessel_id: [],
          office_id: [],
        };

        // Get all vessel IDs
        const vesselIds = filteredGroups
          .filter(group => group.label !== 'Offices')
          .flatMap(group => group.data)
          .map(vessel => Number(vessel.id));

        // Get all office IDs
        const officeIds =
          filteredGroups
            .find(group => group.label === 'Offices')
            ?.data.map(office => office.id as number) || [];

        if (vesselIds.length > 0) {
          newValue.vessel_id = vesselIds;
        }

        if (officeIds.length > 0) {
          newValue.office_id = officeIds;
        }

        onChange(newValue);
      }
    };

    // Close dropdown on outside click
    const dropdownRef = useRef<HTMLDivElement>(null);
    useEffect(() => {
      const handleClickOutside = (event: MouseEvent) => {
        if (
          dropdownRef.current &&
          !dropdownRef.current.contains(event.target as Node)
        ) {
          onClose();
        }
      };
      document.addEventListener('mousedown', handleClickOutside);
      return () =>
        document.removeEventListener('mousedown', handleClickOutside);
    }, [onClose]);

    return (
      <div
        ref={dropdownRef}
        className="option-selector-container option-selector-dropdown"
      >
        <div className="search-bar-section">
          <SearchInput
            placeholder="Search"
            value={search}
            onSearch={e => setSearch(e || '')}
          />
        </div>

        <div className="option-list-section">
          {filteredGroups.length > 0 ? (
            filteredGroups.map(group => (
              <div key={group.label} className="option-group">
                <div className="option-group-label">{group.label}</div>
                {group.data.map(option => (
                  <div
                    key={`${group.label}-${option.id}`}
                    className="option-list-item"
                    role="button"
                    tabIndex={0}
                    onClick={() => handleOptionSelection(group.label, option)}
                    onKeyDown={e => {
                      if (e.key === 'Enter' || e.key === ' ') {
                        e.preventDefault();
                        handleOptionSelection(group.label, option);
                      }
                    }}
                  >
                    <CheckboxComponent
                      id={`option-check-${group.label}-${option.id}`}
                      checked={
                        group.label === 'Offices'
                          ? Boolean(
                              value?.office_id?.includes(Number(option.id)),
                            )
                          : Boolean(
                              value?.vessel_id?.includes(Number(option.id)),
                            )
                      }
                      onChange={() =>
                        handleOptionSelection(group.label, option)
                      }
                      className="option-checkbox"
                    />
                    <div className="option-name">{option.name}</div>
                  </div>
                ))}
              </div>
            ))
          ) : (
            <div className="empty-list-message">No options found.</div>
          )}
        </div>

        <div className="footer-section">
          <button
            type="button"
            className="select-all-link"
            onClick={handleSelectAll}
          >
            {value?.vessel_id || value?.office_id ? 'Clear all' : 'Select all'}
          </button>
        </div>
      </div>
    );
  };

interface VesselAndOfficeDropdownProps {
  value: RAFilterValues['vessel_or_office'];
  options: GroupedOption[];
  placeholder?: string;
  onChange: (selected: RAFilterValues['vessel_or_office']) => void;
  maxDisplayNames?: number; // how many selected names to show before counter
}

const VesselAndOfficeDropdown: React.FC<VesselAndOfficeDropdownProps> = ({
  value,
  options,
  placeholder = 'Select Vessel/Office',
  onChange,
  maxDisplayNames: _maxDisplayNames = 2,
}) => {
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [maxDisplayNames, setMaxDisplayNames] = useState(2);

  const selectedOptions = useMemo(() => {
    const selected = [];
    if (value?.vessel_id) {
      const selectedVessels = options
        .filter(group => group.label !== 'Offices')
        .flatMap(group => group.data)
        .filter(vessel => value.vessel_id?.includes(Number(vessel.id)))
        .map(vessel => ({label: vessel.name, value: vessel.id}));
      selected.push(...selectedVessels);
    }
    if (value?.office_id) {
      const selectedOffices =
        options
          .find(group => group.label === 'Offices')
          ?.data.filter(office =>
            value.office_id?.includes(office.id as number),
          )
          .map(office => ({label: office.name, value: office.id})) || [];
      selected.push(...selectedOffices);
    }
    return selected;
  }, [value, options]);
  const inputRef = useRef<HTMLDivElement>(null);
  const namesMeasureRef = useRef<HTMLSpanElement>(null);
  const counterMeasureRef = useRef<HTMLSpanElement>(null);

  useLayoutEffect(() => {
    if (!inputRef.current || !namesMeasureRef.current) return;
    const inputWidth = inputRef.current.offsetWidth;
    let availableWidth = inputWidth - 40; // leave space for chevron, padding, counter
    if (counterMeasureRef.current && selectedOptions.length > 2) {
      availableWidth -= counterMeasureRef.current.offsetWidth + 8;
    }
    let total = 0;
    let fit = 0;
    const children = Array.from(namesMeasureRef.current.children);
    for (const child of children) {
      const w = (child as HTMLElement).offsetWidth;
      if (total + w > availableWidth) break;
      total += w;
      fit++;
    }
    setMaxDisplayNames(Math.max(1, fit));
  }, [selectedOptions, dropdownOpen]);

  useEffect(() => {
    const handleResize = () => {
      setTimeout(() => {
        if (inputRef.current) {
          setMaxDisplayNames(2); // force re-measure
        }
      }, 100);
    };
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const displayFirstNames = selectedOptions
    .slice(0, maxDisplayNames)
    .map(option => option.label)
    .join(', ');
  const extraCount = selectedOptions.length - maxDisplayNames;
  const extraNames = selectedOptions
    .slice(maxDisplayNames)
    .map(option => option.label);
  const showCounterOnly = selectedOptions.length > maxDisplayNames;

  const handleInputKeyDown = (e: React.KeyboardEvent<HTMLDivElement>) => {
    if (e.key === 'Enter' || e.key === ' ') {
      setDropdownOpen(open => !open);
      e.preventDefault();
    }
    if (e.key === 'Escape') {
      setDropdownOpen(false);
    }
  };

  return (
    <div className="option-multiselect-root typeahead-wrapper">
      <div
        className={classNames(
          'option-multiselect-input typeahead-multiselect form-control',
          dropdownOpen ? ' open' : '',
        )}
        tabIndex={0}
        ref={inputRef}
        onClick={() => setDropdownOpen(open => !open)}
        onKeyDown={handleInputKeyDown}
        aria-haspopup="listbox"
        aria-expanded={dropdownOpen}
        aria-controls="vessel-office-dropdown-listbox"
        role="combobox"
      >
        <span className="option-multiselect-names">
          <span className="option-multiselect-names-list">
            {selectedOptions.length === 0 ? (
              <span className="option-multiselect-placeholder">
                {placeholder}
              </span>
            ) : (
              displayFirstNames
            )}
          </span>
          {showCounterOnly && (
            <span
              className="option-multiselect-counter"
              ref={counterMeasureRef}
            >
              <SingleBadgePopover
                items={extraNames}
                label={`+${extraCount} More`}
              />
            </span>
          )}
        </span>
        <span className="option-multiselect-chevron">
          <ChevronDown size={14} />
        </span>
        <span
          style={{
            position: 'absolute',
            visibility: 'hidden',
            pointerEvents: 'none',
            height: 0,
            whiteSpace: 'nowrap',
          }}
          ref={namesMeasureRef}
        >
          {selectedOptions.map(option => (
            <span key={option.value} style={{marginRight: 4}}>
              {option.label}
              {', '}
            </span>
          ))}
        </span>
      </div>
      {dropdownOpen && (
        <div id="vessel-office-dropdown-listbox" role="listbox">
          <VesselAndOfficeDropdownItem
            options={options}
            value={value || {vessel_id: null, office_id: null}}
            onChange={onChange}
            onClose={() => setDropdownOpen(false)}
          />
        </div>
      )}
    </div>
  );
};

export default VesselAndOfficeDropdown;
