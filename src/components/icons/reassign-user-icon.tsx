import React from 'react';

type SvgUserOutlineProps = React.SVGProps<SVGSVGElement>;

const SvgUserOutline: React.FC<SvgUserOutlineProps> = props => (
  <svg
    width={16}
    height={16}
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M10.7311 12.0822C10.3793 12.0822 10.0931 11.7861 10.0931 11.4221V10.2852C10.0931 9.39535 9.39883 8.67142 8.54541 8.67142H7.45257C6.59916 8.67142 5.90487 9.39535 5.90487 10.2852V11.4221C5.90487 11.7861 5.61867 12.0822 5.26689 12.0822C4.91511 12.0822 4.62891 11.7861 4.62891 11.4221V10.2852C4.63401 9.22239 5.18265 8.2588 6.06849 7.74208C5.66752 7.26499 5.44819 6.65861 5.44854 6.02169C5.44854 4.56071 6.59267 3.37207 7.99899 3.37207C9.40532 3.37207 10.5494 4.56071 10.5494 6.02174C10.5498 6.65861 10.3305 7.26499 9.92949 7.74208C10.8153 8.25879 11.364 9.22236 11.3691 10.2847V11.4221C11.3691 11.7861 11.0829 12.0822 10.7311 12.0822ZM7.99899 4.6922C7.29624 4.6922 6.7245 5.28862 6.7245 6.02174C6.7245 6.75485 7.29624 7.35128 7.99899 7.35128C8.70175 7.35128 9.27349 6.75485 9.27349 6.02174C9.27349 5.28862 8.70175 4.6922 7.99899 4.6922Z"
      fill="#1F4A70"
    />
    <path
      d="M10.7309 11.9904C11.0327 11.9904 11.2773 11.7359 11.2773 11.4219V10.285C11.2722 9.22132 10.6966 8.24963 9.78555 7.76673C10.2173 7.29675 10.458 6.67161 10.4577 6.02155C10.4577 4.60878 9.35676 3.46349 7.99875 3.46349C6.64075 3.46349 5.53986 4.60878 5.53986 6.02155C5.5395 6.67161 5.78025 7.29675 6.21196 7.76673C5.30094 8.24963 4.72534 9.22132 4.72023 10.285V11.4219C4.72023 11.7359 4.96487 11.9904 5.26665 11.9904C5.56842 11.9904 5.81307 11.7359 5.81307 11.4219V10.285C5.81307 9.34314 6.54699 8.57962 7.45233 8.57962H8.54518C9.45052 8.57962 10.1844 9.34314 10.1844 10.285V11.4219C10.1844 11.7359 10.4291 11.9904 10.7309 11.9904ZM7.99875 4.6004C8.75321 4.6004 9.36481 5.23667 9.36481 6.02155C9.36481 6.80643 8.75321 7.4427 7.99875 7.4427C7.2443 7.4427 6.6327 6.80643 6.6327 6.02155C6.6327 5.23667 7.2443 4.6004 7.99875 4.6004ZM10.7309 12.1736C10.3286 12.1736 10.0013 11.8364 10.0013 11.4219V10.285C10.0013 9.44568 9.3481 8.76284 8.54518 8.76284H7.45233C6.64941 8.76284 5.99619 9.44568 5.99619 10.285V11.4219C5.99619 11.8364 5.66892 12.1736 5.26665 12.1736C4.86438 12.1736 4.53711 11.8364 4.53711 11.4219V10.285C4.5422 9.22601 5.07099 8.26261 5.93127 7.71799C5.55896 7.23796 5.35641 6.64376 5.35674 6.02145C5.35674 4.51 6.54195 3.28027 7.99875 3.28027C9.45556 3.28027 10.6408 4.51 10.6408 6.02155C10.6411 6.64377 10.4385 7.23796 10.0662 7.71798C10.9265 8.26259 11.4553 9.22595 11.4604 10.2841V11.4219C11.4604 11.8364 11.1331 12.1736 10.7309 12.1736ZM7.99875 4.78361C7.34648 4.78361 6.81582 5.33895 6.81582 6.02155C6.81582 6.70415 7.34648 7.25949 7.99875 7.25949C8.65103 7.25949 9.18169 6.70415 9.18169 6.02155C9.18169 5.33895 8.65103 4.78361 7.99875 4.78361Z"
      fill="white"
    />
    <path
      d="M7.83973 15.9088C7.83967 15.9088 7.83979 15.9088 7.83973 15.9088L7.8263 15.9088C4.93484 15.9047 2.30839 14.2694 0.971956 11.6409C-0.33784 9.06482 -0.174949 5.99312 1.3883 3.58214H1.2694C0.917619 3.58214 0.631419 3.2897 0.631419 2.93022C0.631419 2.57076 0.917619 2.27832 1.2694 2.27832H2.90866C3.26044 2.27832 3.54664 2.57076 3.54664 2.93022V4.61113C3.54664 4.97059 3.26044 5.26303 2.90866 5.26303C2.55688 5.26303 2.27068 4.97059 2.27068 4.61113V4.58582C1.67138 5.60899 1.35779 6.77314 1.36096 7.9727C1.36005 10.197 2.43667 12.2611 4.2409 13.4945C5.30636 14.2228 6.54641 14.6078 7.82695 14.6078C8.68396 14.6078 9.51973 14.4364 10.3111 14.0985C10.4021 14.0498 10.5043 14.0241 10.607 14.0241C10.7419 14.0241 10.871 14.0668 10.9804 14.1474C11.178 14.2931 11.2761 14.5363 11.2365 14.7821C11.197 15.0266 11.0293 15.2238 10.7983 15.298C9.85654 15.7033 8.86113 15.9088 7.83973 15.9088Z"
      fill="#1F4A70"
    />
    <path
      d="M7.8395 15.817C8.84455 15.817 9.83947 15.6113 10.7661 15.2118C10.9648 15.1498 11.1121 14.9775 11.146 14.7673C11.1799 14.5571 11.0946 14.3454 10.9259 14.221C10.7572 14.0966 10.5343 14.081 10.3508 14.1809C8.32652 15.0468 6.01408 14.8175 4.18911 13.5699C2.36414 12.3224 1.26834 10.2218 1.26926 7.97275C1.26577 6.65243 1.64626 5.36103 2.3621 4.26356V4.61094C2.3621 4.92038 2.60673 5.17124 2.90852 5.17124C3.2103 5.17124 3.45494 4.92038 3.45494 4.61094V2.93003C3.45494 2.62059 3.2103 2.36974 2.90852 2.36974H1.26926C0.967468 2.36974 0.722836 2.62059 0.722836 2.93003C0.722836 3.23948 0.967468 3.49034 1.26926 3.49034H1.55885C-0.0681076 5.88623 -0.262834 9.01039 1.05342 11.5992C2.36968 14.188 4.97914 15.813 7.82631 15.817C7.83069 15.817 7.83513 15.817 7.8395 15.817ZM7.83958 16.0002L7.8258 16.0002C4.89999 15.9961 2.24233 14.3415 0.890205 11.6822C-0.409271 9.12646 -0.2756 6.08858 1.2226 3.67205C0.841977 3.64746 0.539719 3.32405 0.539719 2.93003C0.539719 2.52007 0.866986 2.18652 1.26926 2.18652H2.90852C3.31079 2.18652 3.63806 2.52007 3.63806 2.93003V4.61094C3.63806 5.02091 3.31079 5.35445 2.90852 5.35445C2.59231 5.35445 2.32244 5.14836 2.22145 4.86105C1.71395 5.81323 1.44948 6.87783 1.45237 7.97226C1.45148 10.1665 2.51317 12.2024 4.29241 13.4187C5.3426 14.1366 6.56477 14.516 7.82681 14.516C8.6701 14.516 9.49259 14.3476 10.2715 14.0155C10.3748 13.9611 10.4905 13.9323 10.6068 13.9323C10.7614 13.9323 10.9093 13.9811 11.0345 14.0735C11.2599 14.2397 11.3719 14.5167 11.3267 14.7965C11.282 15.0738 11.0922 15.2979 10.8303 15.3836C9.87825 15.7928 8.87208 16.0002 7.83958 16.0002Z"
      fill="white"
    />
    <path
      d="M14.7308 13.7222H13.0915C12.7398 13.7222 12.4536 13.4298 12.4536 13.0703V11.3894C12.4536 11.03 12.7398 10.7375 13.0915 10.7375C13.4433 10.7375 13.7295 11.03 13.7295 11.3894V11.4148C14.3288 10.3916 14.6424 9.22745 14.6393 8.02788C14.6402 5.80358 13.5635 3.73946 11.7593 2.50609C10.6939 1.77776 9.45385 1.3928 8.17326 1.3928C7.31494 1.3928 6.47786 1.5647 5.68527 1.90372L5.681 1.90556L5.67656 1.90694C5.61585 1.9259 5.55308 1.93552 5.49 1.93552C5.23049 1.93552 4.99896 1.77698 4.90014 1.53163C4.77472 1.22033 4.89945 0.86627 5.19027 0.70806L5.19394 0.706069L5.19777 0.704408C6.14077 0.29791 7.13758 0.0917969 8.16057 0.0917969L8.17392 0.0918091C11.0654 0.0958642 13.6918 1.73121 15.0283 4.35967C16.3381 6.93574 16.1752 10.0075 14.6119 12.4184H14.7308C15.0826 12.4184 15.3688 12.7109 15.3688 13.0703C15.3688 13.4298 15.0826 13.7222 14.7308 13.7222Z"
      fill="#1F4A70"
    />
    <path
      d="M14.7306 13.6304C15.0324 13.6304 15.277 13.3796 15.277 13.0702C15.277 12.7607 15.0324 12.5098 14.7306 12.5098H14.441C16.0679 10.114 16.2627 6.98979 14.9464 4.40101C13.6302 1.81223 11.0207 0.187218 8.17354 0.183224C7.16411 0.181409 6.16442 0.387176 5.23379 0.788348C4.98513 0.923619 4.87737 1.23044 4.98484 1.49721C5.09229 1.76397 5.3798 1.9034 5.64906 1.8193C7.67334 0.95342 9.98578 1.18271 11.8107 2.43025C13.6357 3.6778 14.7315 5.77839 14.7306 8.02744C14.7341 9.34775 14.3536 10.6392 13.6378 11.7366V11.3892C13.6378 11.0798 13.3931 10.8289 13.0913 10.8289C12.7896 10.8289 12.5449 11.0798 12.5449 11.3892V13.0702C12.5449 13.3796 12.7896 13.6304 13.0913 13.6304H14.7306ZM14.7306 13.8137H13.0913C12.6891 13.8137 12.3618 13.4801 12.3618 13.0702V11.3892C12.3618 10.9793 12.6891 10.6457 13.0913 10.6457C13.4075 10.6457 13.6774 10.8518 13.7784 11.1391C14.2859 10.187 14.5504 9.12238 14.5475 8.02793C14.5484 5.83364 13.4867 3.79781 11.7074 2.58153C10.6573 1.86365 9.43513 1.48421 8.17304 1.48421C7.32717 1.48421 6.5022 1.65363 5.72105 1.98776L5.70363 1.99419C5.63408 2.01591 5.56213 2.02693 5.48979 2.02693C5.19273 2.02693 4.92786 1.84588 4.81499 1.56569C4.67219 1.21121 4.81463 0.807829 5.14632 0.62739L5.16134 0.620086C6.11582 0.208629 7.12484 0 8.16036 0L8.17405 1.22307e-05C11.0999 0.00411621 13.7575 1.65865 15.1096 4.31795C16.4091 6.87373 16.2754 9.9116 14.7772 12.3281C15.1579 12.3527 15.4601 12.6761 15.4601 13.0702C15.4601 13.4801 15.1329 13.8137 14.7306 13.8137Z"
      fill="white"
    />
  </svg>
);

export default SvgUserOutline;
