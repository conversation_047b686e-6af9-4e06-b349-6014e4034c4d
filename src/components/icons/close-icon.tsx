import React from 'react';

interface CloseIconProps extends React.SVGProps<SVGSVGElement> {
  size?: number;
  color?: string;
}

const CloseIcon: React.FC<CloseIconProps> = ({
  size = 20,
  color = '#1F4A70',
  ...props
}) => {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M16.6978 4.60473C16.8811 4.41197 16.9939 4.15059 16.9939 3.86293C16.9939 3.59016 16.8926 3.34115 16.7255 3.15135L16.7266 3.15252C16.5567 2.9593 16.3091 2.83789 16.033 2.83789C15.7737 2.83789 15.5393 2.94504 15.3718 3.11744L9.42328 9.26771C9.25256 9.46246 9.14844 9.71934 9.14844 10.0005C9.14844 10.2982 9.26523 10.5687 9.45547 10.7686L9.45502 10.7681L15.3716 16.8814C15.5394 17.0546 15.7741 17.1621 16.0339 17.1621C16.3095 17.1621 16.5567 17.0412 16.7257 16.8496L16.7266 16.8486C16.892 16.6601 16.9929 16.4115 16.9929 16.1393C16.9929 15.851 16.8797 15.5892 16.6954 15.3959L16.6959 15.3963L11.7952 10.2407C11.7366 10.178 11.7007 10.0935 11.7007 10.0006C11.7007 9.90768 11.7367 9.82313 11.7954 9.7602L11.7952 9.76041L16.6978 4.60473Z"
        fill={color}
      />
      <path
        d="M3.30195 4.60473C3.11865 4.41197 3.00586 4.15059 3.00586 3.86293C3.00586 3.59016 3.10723 3.34115 3.27426 3.15135L3.2732 3.15252C3.44305 2.9593 3.69068 2.83789 3.96676 2.83789C4.22613 2.83789 4.46049 2.94504 4.62795 3.11744L10.5765 9.26771C10.7472 9.46246 10.8513 9.71934 10.8513 10.0005C10.8513 10.2982 10.7346 10.5687 10.5443 10.7686L10.5448 10.7681L4.6282 16.8814C4.46039 17.0546 4.22572 17.1621 3.96586 17.1621C3.69033 17.1621 3.44305 17.0412 3.2741 16.8496L3.27322 16.8486C3.10777 16.6601 3.00684 16.4115 3.00684 16.1393C3.00684 15.851 3.12004 15.5892 3.30436 15.3959L3.30393 15.3963L8.20863 10.2407C8.26723 10.178 8.30314 10.0935 8.30314 10.0006C8.30314 9.90768 8.26715 9.82313 8.20844 9.7602L8.20863 9.76041L3.30195 4.60473Z"
        fill={color}
      />
    </svg>
  );
};

export default CloseIcon;
