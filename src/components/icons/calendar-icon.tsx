import React from 'react';

interface CalendarIconProps extends React.SVGProps<SVGSVGElement> {}

const CalendarIcon2: React.FC<CalendarIconProps> = props => (
  <svg
    width="15"
    height="14"
    viewBox="0 0 15 14"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props} // Spread props to allow overriding default attributes
  >
    <path
      d="M11.4668 0.300781C11.6875 0.300993 11.8661 0.479507 11.8662 0.700195V1.80078H12.8662C13.805 1.80078 14.5662 2.56129 14.5664 3.5V12.1006C14.5663 13.0394 13.805 13.8008 12.8662 13.8008H2.66602C1.72737 13.8006 0.966903 13.0393 0.966797 12.1006V3.5C0.967008 2.56142 1.72744 1.80099 2.66602 1.80078H4.06641V0.700195C4.06651 0.479377 4.24595 0.300781 4.4668 0.300781C4.68746 0.300992 4.86611 0.479507 4.86621 0.700195V1.80078H11.0664V0.700195C11.0665 0.479376 11.246 0.300781 11.4668 0.300781ZM1.7666 12.1006C1.76671 12.5974 2.16921 12.9998 2.66602 13H12.8662C13.3632 13 13.7665 12.5975 13.7666 12.1006V5.30078H1.7666V12.1006ZM2.66602 2.60059C2.16927 2.6008 1.76681 3.00326 1.7666 3.5V4.5H13.7666V3.5C13.7664 3.00313 13.3631 2.60059 12.8662 2.60059H2.66602Z"
      fill="#6C757D" // Default fill color from SVG
      stroke="#6C757D" // Default stroke color from SVG
      strokeWidth="0.2" // Default stroke width from SVG
    />
  </svg>
);

export default CalendarIcon2;
