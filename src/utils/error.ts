import {AxiosError} from 'axios';

export const extractErrorMessage = (error: any) => {
  let message = 'Something went wrong. Please try again';

  if (error?.message) message = error.message;

  if (error?.isAxiosError) {
    const axiosError: AxiosError<any> = error;

    if (axiosError.message) message = axiosError.message;

    if (axiosError.response?.data?.message)
      message = axiosError.response.data.message;
  }

  return message;
};
