$primary-blue: #007bff;
$light-gray-border: #dee2e6;
$item-separator-border: #f0f0f0;
$avatar-bg: #e0f2ff; // Light blue for avatar background
$avatar-text-color: $primary-blue;
$text-color-primary: #212529;
$text-color-secondary: #6c757d;
$container-bg: #fff;

.user-multiselect-root {
  position: relative;
  width: 100%;
  max-width: 500px;
}

.user-multiselect-input {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 6px 8px 6px 12px;
  gap: 10px;
  height: 32px;
  width: 100%;
  border: 1px solid #ced4da;
  border-radius: 0.25rem;

  cursor: pointer;
  font-weight: 400;
  outline: none;
  position: relative;
  transition: border-color 0.15s;

  &:focus,
  &.open {
    border-color: $primary-blue;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.13);
  }

  .user-multiselect-names {
    flex: 1 1 auto;
    min-width: 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    color: #333333;
    display: flex;
    align-items: center;
    font-size: 1rem;
    font-weight: 500;

    .user-multiselect-placeholder {
      color: #757575;
      font-weight: 400;
    }

    .user-multiselect-counter {
      margin-left: 8px;
      background: #f2f5f7;
      color: #111315;
      font-size: 1rem;
      border-radius: 18px;
      padding: 2px 12px;
      font-weight: 400;
      display: inline-block;
      vertical-align: middle;
    }
  }

  .user-multiselect-chevron {
    margin-left: 8px;
    color: #fff;
    font-size: 18px;
    display: flex;
    align-items: center;
  }
}

.user-selector-dropdown {
  position: absolute;
  top: calc(100% + 4px);
  left: 0;
  width: 100%;
  z-index: 1000;
  background: $container-bg;
  border: 1px solid $light-gray-border;
  border-radius: 9px;
  box-shadow: 0 4px 14px rgba(0, 0, 0, 0.07);
  margin-top: 4px;
  padding: 0;
}

.user-selector-container {
  border: 1px solid $light-gray-border;
  border-radius: 0.3rem;
  background-color: $container-bg;
  max-width: 350px;

  .search-bar-section {
    padding: 8px;

    .search-input-wrapper {
      position: relative;

      .search-icon {
        position: absolute;
        left: 10px;
        top: 50%;
        transform: translateY(-50%);
        z-index: 2;
        pointer-events: none;
      }

      input.form-control {
        padding-left: 32px;
        border-radius: 5px;
        background: #f5f7fa;
        border: 1px solid #e4e8ee;
        font-size: 1rem;
        font-weight: 400;
        min-height: 38px;
        color: #222c3a;

        &:focus {
          box-shadow: none;
          border-color: #b1c7de;
        }
      }
    }
  }

  .user-list-section {
    max-height: 250px;
    overflow-y: auto;
    padding: 0 8px 8px 8px;

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
    }

    &::-webkit-scrollbar-thumb {
      background: #d1d1d1;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb:hover {
      background: #b1b1b1;
    }
  }

  .user-item {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    border-bottom: 1px solid $item-separator-border;
    cursor: pointer;
    background-color: $container-bg;

    &:last-child {
      border-bottom: none;
    }

    .form-check-input {
      margin-top: 0;
      margin-right: 1rem;
      border-color: #adb5bd;

      &:checked {
        background-color: $primary-blue;
        border-color: $primary-blue;
      }

      &:focus {
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
      }
    }

    .avatar {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background-color: $avatar-bg;
      color: $avatar-text-color;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 500;
      font-size: 0.9rem;
      margin-right: 1rem;
      flex-shrink: 0;
    }

    .user-info {
      display: flex;
      flex-direction: column;
      overflow: hidden;

      .user-name {
        font-weight: 500;
        color: $text-color-primary;
        font-size: 0.95rem;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .user-details {
        font-size: 0.8rem;
        color: $text-color-secondary;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }

  .empty-list-message {
    padding: 1rem;
    text-align: center;
    color: $text-color-secondary;
  }

  .footer-section {
    padding: 0.75rem 1rem;
    border-top: 1px solid $light-gray-border;

    .select-all-link {
      color: $primary-blue;
      text-decoration: none;
      cursor: pointer;
      font-size: 0.9rem;
      font-weight: 500;
      background: none;
      border: none;
      padding: 0;

      &:hover {
        text-decoration: underline;
      }
    }
  }
}
