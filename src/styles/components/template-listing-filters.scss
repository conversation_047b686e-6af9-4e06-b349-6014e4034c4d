.ra-listing-filters {
  margin-bottom: 4px;

  .equal-width-cols {
    display: flex;
    flex-wrap: wrap;
    margin-left: -6px;
    margin-right: -6px;

    .col-equal {
      padding: 0px;
      padding: 0 6px;
      margin-bottom: 12px;

      // First column
      &:first-child {
        flex: 0 0 calc((100% - 136px) / 3);
        max-width: calc((100% - 136px) / 3);
      }

      // Middle columns
      &:not(:first-child):not(:last-child) {
        flex: 0 0 calc(((100% - 136px) - ((100% - 136px) / 3)) / 3);
        max-width: calc(((100% - 136px) - ((100% - 136px) / 3)) / 3);
      }

      // Button column
      &:last-child {
        flex: 0 0 136px;
        max-width: 136px;
      }

      // Responsive: 3 columns per row
      @media (max-width: 1400px) {
        &:first-child {
          flex: 0 0 calc((100% - 136px) / 3);
          max-width: calc((100% - 136px) / 3);
        }

        &:not(:first-child):not(:last-child) {
          flex: 0 0 calc(((100% - 136px) - ((100% - 136px) / 3)) / 3);
          max-width: calc(((100% - 136px) - ((100% - 136px) / 3)) / 3);
        }

        &:last-child {
          flex: 0 0 136px;
          max-width: 136px;
        }
      }

      // Responsive: 2 columns per row
      @media (max-width: 1050px) {
        &:first-child {
          flex: 0 0 calc(100% / 2);
          max-width: calc(100% / 2);
        }

        &:not(:first-child):not(:last-child) {
          flex: 0 0 calc(100% / 2);
          max-width: calc(100% / 2);
        }

        &:last-child {
          flex: 0 0 136px;
          max-width: 136px;
          margin-right: auto;
        }
      }

      // Responsive: 1 column per row
      @media (max-width: 575px) {
        &:first-child {
          flex: 0 0 100%;
          max-width: 100%;
        }

        &:not(:first-child):not(:last-child) {
          flex: 0 0 100%;
          max-width: 100%;
        }

        &:last-child {
          flex: 0 0 136px;
          max-width: 136px;
          margin-right: auto;
        }
      }
    }
  }
}
