.assign-approvers-card {
  .card-body {
    overflow: hidden !important;
    justify-content: start !important;
    display: flex;
    flex-direction: column;
    padding: 0px !important;
    min-height: 200px;
    max-height: 339px;
  }

  hr {
    margin-bottom: 0px;
  }

  .assign-btn {
    all: unset;
    cursor: pointer;
    box-sizing: border-box;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    padding: 4px 8px;
    gap: 2px;

    width: 61px;
    height: 28px;

    background: #ffffff;
    border: 1px solid #1f4a70;
    border-radius: 4px;
    font-weight: 400;
    font-size: 14px;
    line-height: 20px;

    color: #1f4a70;

    &:hover,
    &:focus,
    &:active {
      background-color: darken(#ffffff, 10%) !important;
    }

    &:disabled {
      background: #efefef !important;
      border: 1px solid #efefef !important;
      border-radius: 4px !important;
      font-size: 14px !important;
      color: #aaaaaa !important;
      cursor: not-allowed !important;
    }
  }
}

.assign-approvers-container {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 0px 16px;
  gap: 10px;
  overflow-y: auto;

  .assign-approver {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    padding: 8px 0px;
    gap: 8px;

    .condition-for-action-heading {
      font-weight: 500;
      font-size: 14px;
      line-height: 20px;
      color: #333333;
    }
    .condition-for-action-content {
      font-weight: 400;
      font-size: 14px;
      line-height: 20px;
      color: #333333;
    }
  }

  .approver-title {
    font-weight: 500;
    font-size: 16px;
    line-height: 24px;
    color: #333333;
  }

  .assign-approver-input {
    width: 100%;
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 8px;
    justify-content: space-between;
    max-width: 375px;
    height: 32px;
  }

  .user-list-item {
    display: flex !important;
    align-items: center !important;
    width: 100%;

    .avatar {
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;
      margin-right: 12px;

      width: 48px;
      height: 48px;

      background: #e5f4f8;
      font-size: 16px;
      font-weight: 500;
      line-height: 24px;
      text-align: center;
      color: #1f4a70;
    }
    .user-info {
      display: flex;
      flex-direction: column;
      overflow: hidden;

      .user-name {
        font-weight: 500;
        font-size: 16px;
        line-height: 24px;

        color: #333333;

        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .user-details {
        font-weight: 400;
        font-size: 14px;
        line-height: 20px;
        overflow: hidden;

        color: #6c757d;

        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
  }

  .basic-btn {
    padding: 6px 12px;
    height: 36px;
    font-weight: 400;
    font-size: 16px;
  }

  .approval-btn {
    display: inline-block;
    box-sizing: border-box;

    button.dropdown-toggle {
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;
      padding: 4px 8px;
      gap: 2px;
      background: #173652;
      border: 1px solid #173652;
      border-radius: 4px;
      font-weight: 400;
      font-size: 14px;
      line-height: 20px;
      color: #ffffff;
      height: 28px;
    }

    .dropdown-toggle:focus,
    .dropdown-toggle:active {
      box-shadow: 0 0 0 0.2rem rgba(23, 54, 82, 0.25);
      outline: none;
    }

    .dropdown-toggle::after {
      display: inline-block;
      margin-left: 0.255em;
      vertical-align: middle;
      content: '';
      border-top: 0.3em solid;
      border-right: 0.3em solid transparent;
      border-bottom: 0;
      border-left: 0.3em solid transparent;
    }
  }
}
