.ra-single-badge-popover-container {
  display: inline-flex;
  flex-direction: row;
  align-items: center;
  padding: 2px 8px;
  gap: 10px;
  height: 20px;
  background: #edf3f7;
  border-radius: 50px;
  flex: none;
  order: 0;
  flex-grow: 0;
}

.ra-single-badge-popover-button {
  font-weight: 400;
  font-size: 12px;
  line-height: 16px;
  color: #333333;
  white-space: nowrap;
}

.ra-single-badge-popover {
  background: #000000;
  border-radius: 4px;
  border: none;
  padding: 4px 8px;
  margin: 0;

  .popover-arrow {
    position: absolute;
    width: 0;
    height: 0;
    border: 8px solid transparent;

    &::before {
      display: none;
    }
  }

  &.bs-popover-end {
    margin-left: 8px;
    > .popover-arrow {
      left: -8px;
      top: calc(50% - 8px);
      border-right-color: #000000;
      border-left-width: 0;
    }
  }

  &.bs-popover-start {
    margin-right: 8px;
    > .popover-arrow {
      right: -8px;
      top: calc(50% - 8px);
      border-left-color: #000000;
      border-right-width: 0;
    }
  }

  &.bs-popover-top {
    margin-bottom: 8px;
    > .popover-arrow {
      bottom: -8px;
      left: calc(50% - 8px);
      border-top-color: #000000;
      border-bottom-width: 0;
    }
  }

  &.bs-popover-bottom {
    margin-top: 8px;
    > .popover-arrow {
      top: -8px;
      left: calc(50% - 8px);
      border-bottom-color: #000000;
      border-top-width: 0;
    }
  }

  .ra-single-badge-popover-body {
    font-weight: 400;
    font-size: 14px;
    line-height: 20px;
    color: #ffffff !important;
    padding: 4px;
  }
}
