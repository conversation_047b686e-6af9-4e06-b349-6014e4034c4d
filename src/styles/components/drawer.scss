.ra-drawer {
  position: fixed;
  top: 62px;
  right: 0;
  width: 411px;
  height: calc(100vh - 62px);
  display: flex;
  flex-direction: column;
  z-index: 2000;
  overflow: hidden;

  background: #ffffff;
  border: 1px solid #cccccc;
  border-radius: 4px;
  border-right: none;
  border-top-right-radius: 0px;
  border-bottom-right-radius: 0px;
}

.ra-drawer-header {
  margin: 20px;

  display: flex;
  justify-content: space-between;
  align-items: center;

  font-weight: 600;
  font-size: 16px;
  line-height: 24px;

  color: #333333;
}

.ra-drawer-close {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 20px !important;
  height: 20px !important;
  cursor: pointer !important;
  margin-left: auto !important;
}

.ra-drawer-content {
  flex: 1; /* allows the content to take up remaining space */
  display: flex;
  flex-direction: column;
  overflow: hidden;
}
