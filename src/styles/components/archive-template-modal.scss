.archive-template-modal {
  background: #ffffff;
}

.modal-header {
  font-weight: 500;
  font-size: 20px;
  line-height: 28px;
  color: #333333;
  padding: 20px 20px 16px;
  width: 100%;
  border-bottom: 1px solid #dee2e6;
}

.modal-content-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 16px 20px;
  gap: 16px;
  width: 100%;
}

.alert-large {
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 12px 16px;
  gap: 10px;

  background: #fff9e8;
  border-radius: 4px;
}

.alert-text {
  font-size: 14px;
  line-height: 20px;
  color: #bf7f05;
}

.template-detail-card {
  width: 100% !important;
}

.template-detail-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  width: 100%;
  border-top: 1px solid #cccccc;
}

.last-updated {
  font-weight: 400;
  font-size: 12px;
  line-height: 16px;
  color: #6c757d;
}

.button-group {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  align-items: center;
  padding: 16px 20px 20px;
  gap: 8px;

  width: 100%;

  border-top: 1px solid #dee2e6;
}

button.btn-secondary {
  background: #0091b8;
  border: 1px solid #0091b8;
  border-radius: 4px;
  font-size: 14px;
  color: #ffffff;
}

button.btn-primary {
  background: #1f4a70;
  border: 1px solid #1f4a70;
  border-radius: 4px;
  font-size: 14px;
  color: #ffffff;
}
