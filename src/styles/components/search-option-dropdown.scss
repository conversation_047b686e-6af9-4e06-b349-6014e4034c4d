.option-multiselect-root {
  position: relative;
  width: 100%;
}

.option-multiselect-input {
  display: flex;
  align-items: center;
  padding: 6px 12px;
  width: 100%;
  box-sizing: border-box;
  gap: 0;

  .option-multiselect-names {
    flex: 1 1 0%;
    min-width: 0;
    overflow: hidden;
    display: flex;
    align-items: center;
    font-weight: 400;
    font-size: 14px;
    line-height: 20px;

    .option-multiselect-placeholder {
      color: #757575;
      font-weight: 400;
    }

    .option-multiselect-names-list {
      flex: 1 1 0%;
      min-width: 0;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  .option-multiselect-counter {
    margin-left: 4px;
    flex-shrink: 0;
    display: inline-block;
  }

  .option-multiselect-icons {
    display: flex;
    align-items: center;
    gap: 8px;

    .option-multiselect-clear {
      display: flex;
      align-items: center;
      cursor: pointer;
      padding: 2px;
      border-radius: 50%;

      &:hover {
        background-color: rgba(0, 0, 0, 0.04);
      }

      svg {
        color: #757575;
      }
    }

    .option-multiselect-chevron {
      flex-shrink: 0;
      display: flex;
      align-items: center;
    }
  }
}

.option-selector-dropdown {
  position: absolute;
  top: calc(100% + 1px);
  left: 0;
  width: 100%;
  min-width: max-content;
  z-index: 1000;
  background: #fff;
  border: 1px solid #dbe2ea;
  border-radius: 4px;
  box-shadow: 0 4px 14px rgba(0, 0, 0, 0.15);
  margin-top: 4px;
  padding: 0;
  max-height: 80vh;
  overflow-y: auto;

  &[data-popper-placement='top'] {
    margin-top: 0;
    margin-bottom: 4px;
  }
}

.option-selector-container {
  background: #ffffff;
  border: 1px solid #cccccc;
  border-radius: 4px;
  overflow: hidden;

  .search-bar-section {
    padding: 8px;
  }

  .option-list-section {
    max-height: 306px;
    overflow-y: auto;
    padding: 0 8px;
    margin-bottom: 8px;

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background: #d9d9d9;
      border-radius: 6px;
    }

    .option-checkbox {
      margin-bottom: 0 !important;
    }
  }

  .option-list-item {
    display: flex !important;
    align-items: center !important;
    padding: 8px !important;
    cursor: pointer !important;
    transition: background 0.1s !important;

    &:hover {
      background: #f8fafc !important;
    }

    &.single-select {
      .option-name {
        padding-left: 0px !important;
        margin-left: 0px !important;
      }

      &:hover {
        background: #f0f7ff !important;
      }

      &.selected {
        background: #e6f0ff !important;
      }
    }

    .option-info {
      display: flex;
      flex-direction: column;
      overflow: hidden;
    }

    .option-name {
      color: #333333;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      font-weight: 400;
      font-size: 14px;
      line-height: 20px;
    }

    .option-details {
      font-weight: 400;
      font-size: 14px;
      line-height: 20px;
      color: #6c757d;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

  .empty-list-message {
    padding: 1rem;
    font-weight: 400;
    font-size: 14px;
    line-height: 20px;
    color: #6c757d;
  }

  .footer-section {
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 8px 8px 12px 16px;
    gap: 10px;

    margin: 0 auto;
    height: 36px;

    border-top: 1px solid #cccccc;

    button {
      all: unset;
      color: #1f4a70;
      text-decoration-color: #1f4a70;
      cursor: pointer;

      font-weight: 400;
      font-size: 14px;
      line-height: 20px;
      text-decoration-line: underline;
      border: 0px;
    }
  }
}
