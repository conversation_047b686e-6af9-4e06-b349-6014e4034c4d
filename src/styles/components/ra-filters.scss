.ra-filters {
  margin-bottom: 4px;

  .equal-width-cols {
    display: flex;
    flex-wrap: wrap;
    margin-left: -6px;
    margin-right: -6px;

    .col-equal {
      padding: 0px;
      padding: 0 6px;
      margin-bottom: 12px;

      // First column - slightly larger
      &:first-child {
        flex: 0 0 calc((100% - 136px) * 0.22);
        max-width: calc((100% - 136px) * 0.22);
      }

      // Middle columns (2-6)
      &:not(:first-child):not(:last-child) {
        flex: 0 0 calc(((100% - 136px) * 0.78) / 5);
        max-width: calc(((100% - 136px) * 0.78) / 5);
      }

      // Button column
      &:last-child {
        flex: 0 0 136px;
        max-width: 136px;
      }

      // Responsive: Full columns layout (1400px+)
      @media (max-width: 1600px) {
        &:first-child {
          flex: 0 0 calc((100% - 136px) * 0.22);
          max-width: calc((100% - 136px) * 0.22);
        }

        &:not(:first-child):not(:last-child) {
          flex: 0 0 calc(((100% - 136px) * 0.78) / 5);
          max-width: calc(((100% - 136px) * 0.78) / 5);
        }

        &:last-child {
          flex: 0 0 136px;
          max-width: 136px;
        }
      }

      // Responsive: 3-4 columns per row
      @media (max-width: 1400px) {
        &:first-child {
          flex: 0 0 calc(100% / 3);
          max-width: calc(100% / 3);
        }

        &:not(:first-child):not(:last-child) {
          flex: 0 0 calc(100% / 3);
          max-width: calc(100% / 3);
        }

        &:last-child {
          flex: 0 0 100%;
          max-width: 100%;
          margin-left: auto;
        }
      }

      // Responsive: 2 columns per row
      @media (max-width: 1050px) {
        &:first-child {
          flex: 0 0 calc(100% / 2);
          max-width: calc(100% / 2);
        }

        &:not(:first-child):not(:last-child) {
          flex: 0 0 calc(100% / 2);
          max-width: calc(100% / 2);
        }

        &:last-child {
          flex: 0 0 100%;
          max-width: 100%;
          margin-left: auto;
        }
      }

      // Responsive: 1 column per row
      @media (max-width: 575px) {
        &:first-child,
        &:not(:first-child):not(:last-child) {
          flex: 0 0 100%;
          max-width: 100%;
        }

        &:last-child {
          flex: 0 0 100%;
          max-width: 100%;
          margin-left: auto;
        }
      }
    }
  }

  .more-filters-button {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    padding: 6px 12px;
    gap: 2px;

    width: 125px;
    height: 32px;

    background: #ffffff !important;
    border: 1px solid #1f4a70;
    border-radius: 4px;

    .icon {
      width: 20px;
      height: 20px;

      flex: none;
      order: 0;
      flex-grow: 0;
      margin-right: 2px;
      color: #1f4a70;
    }

    .label {
      width: 79px;
      height: 20px;

      font-weight: 400;
      font-size: 14px;
      line-height: 20px;
      /* identical to box height, or 143% */
      display: flex;
      align-items: center;
      text-align: center;

      color: #1f4a70;

      /* Inside auto layout */
      flex: none;
      order: 1;
      flex-grow: 0;
    }

    .label-badge {
      display: flex;
      flex-direction: row;
      align-items: center;
      padding: 2px 8px;

      width: 24px;
      height: 20px;

      background: #edf3f7;
      border-radius: 50px;

      font-weight: 500;
      font-size: 12px;
      line-height: 16px;
      margin-left: 12px;

      color: #333333;
    }
  }
}
