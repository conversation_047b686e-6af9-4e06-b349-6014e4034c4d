import axios, {AxiosHeaders} from 'axios';
import {getToken} from '@paris2/auth';

const HttpMethods = {
  GET: 'GET',
  POST: 'POST',
  DELETE: 'DELETE',
};

// Store cancellation controllers for specific endpoints
const cancelControllers = new Map<string, AbortController>();

// Helper function to manage request cancellation
const cancelPreviousRequest = (endpoint: string) => {
  if (cancelControllers.has(endpoint)) {
    const controller = cancelControllers.get(endpoint);
    controller?.abort();
    cancelControllers.delete(endpoint);
  }
  const newController = new AbortController();
  cancelControllers.set(endpoint, newController);
  return newController.signal;
};

const _axios = axios.create();

// Request interceptor
_axios.interceptors.request.use(
  async config => {
    // Add authorization header
    const token = await getToken();
    if (token) {
      const headers = new AxiosHeaders({
        'Content-Type': 'application/json',
        Authorization: `Bearer ${token}`,
        ...config.headers, // Preserve other headers including X-Cancel-Key
      });
      config.headers = headers;
    }
    return config;
  },
  error => {
    return Promise.reject(error);
  },
);

// Response interceptor
_axios.interceptors.response.use(
  response => {
    return response;
  },
  error => {
    // Don't treat cancellations as errors that need to be handled
    if (axios.isCancel(error)) {
      return Promise.reject(new Error('Request cancelled'));
    }
    return Promise.reject(error);
  },
);

const getAxiosClient = () => _axios;

export default {
  HttpMethods,
  getAxiosClient,
  axios,
  cancelPreviousRequest, // Export the cancellation function
};
