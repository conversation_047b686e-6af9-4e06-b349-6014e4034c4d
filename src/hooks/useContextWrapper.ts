import React, {useContext} from 'react';

interface IConfig {
  contextName: string;
  providerName: string;
}

/**
 * The `useContextWrapper` function wraps the `useContext` hook and
 * throws an error if the context is not provided.
 * @param ReactContext - The `ReactContext` parameter is a generic type that represents a React
 * context. It is used to access the value provided by a context provider.
 * @param {IConfig} config - The `config` parameter is an object that contains two properties:
 * @returns The `context` variable is being returned.
 */
const useContextWrapper = <T>(
  ReactContext: React.Context<T>,
  config: IConfig,
) => {
  const context = useContext(ReactContext);
  const {contextName, providerName} = config;

  if (!context) {
    throw new Error(`${contextName} must be used within a ${providerName}`);
  }

  return context;
};

export default useContextWrapper;
