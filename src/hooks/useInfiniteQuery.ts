import {useState, useEffect, useCallback, useMemo} from 'react';

type ApiResponse<T, R extends object = {}> = {
  data: T[];
  pagination: {
    totalItems: number;
    totalPages: number;
    page: number;
    pageSize: number;
  };
} & R;

type UseInfiniteQueryOptions = {
  page?: number;
  limit?: number;
} & Record<string, any>;

interface UseInfiniteQueryResult<T, R extends object = {}> {
  data: ApiResponse<T, R>;
  isLoading: boolean;
  isFetchingNextPage: boolean;
  fetchNextPage: () => Promise<void>;
  hasNextPage: boolean;
  error: Error | null;
  refetch: () => void;
  reset: () => void;
}

const useInfiniteQuery = <T, R extends object = {}>(
  fetchFn: (params: {
    page: number;
    limit: number;
  }) => Promise<ApiResponse<T, R>>,
  {page: initialPage = 1, limit = 50, ...rest}: UseInfiniteQueryOptions = {},
): UseInfiniteQueryResult<T, R> => {
  const [data, setData] = useState<ApiResponse<T, R>>({
    data: [],
    pagination: {totalItems: 0, totalPages: 0, page: 0, pageSize: 0},
  } as ApiResponse<T, R>);
  const [page, setPage] = useState(initialPage);
  const [isLoading, setIsLoading] = useState(false);
  const [isFetchingNextPage, setIsFetchingNextPage] = useState(false);
  const [hasNextPage, setHasNextPage] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  // Memoize rest object to prevent unnecessary re-renders
  const memoizedRest = useMemo(() => rest, [JSON.stringify(rest)]);

  const fetchData = useCallback(async () => {
    try {
      setIsLoading(true);
      const response = await fetchFn({
        page: initialPage,
        limit,
        ...memoizedRest,
      });
      setData(response);
      setHasNextPage(response.pagination.page < response.pagination.totalPages);
    } catch (err) {
      setError(err as Error);
    } finally {
      setIsLoading(false);
    }
  }, [fetchFn, initialPage, limit, memoizedRest]);

  const fetchNextPage = useCallback(async () => {
    if (!hasNextPage || isFetchingNextPage) return;

    try {
      setIsFetchingNextPage(true);
      const response = await fetchFn({page: page + 1, limit, ...memoizedRest});
      setData(prev => ({...response, data: [...prev.data, ...response.data]}));
      setPage(prev => prev + 1);
      setHasNextPage(response.pagination.page < response.pagination.totalPages);
    } catch (err) {
      setError(err as Error);
    } finally {
      setIsFetchingNextPage(false);
    }
  }, [fetchFn, page, limit, memoizedRest, hasNextPage, isFetchingNextPage]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  const refetch = useCallback(() => {
    fetchData();
  }, [fetchData]);

  const reset = useCallback(() => {
    setPage(initialPage);
    fetchData();
  }, [initialPage, fetchData]);

  return {
    data,
    isLoading,
    isFetchingNextPage,
    fetchNextPage,
    hasNextPage,
    error,
    refetch,
    reset,
  };
};

export default useInfiniteQuery;
