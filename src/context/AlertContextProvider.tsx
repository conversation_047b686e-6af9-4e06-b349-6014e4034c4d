import React, {createContext, useCallback, useMemo, useState} from 'react';
import {Alert} from 'react-bootstrap';
import {useContextWrapper} from '../hooks';

interface IAlertDetails {
  type:
    | 'primary'
    | 'secondary'
    | 'success'
    | 'danger'
    | 'warning'
    | 'info'
    | 'light'
    | 'dark';
  message: string;
}

interface IAlertContext {
  alert(type: IAlertDetails['type'], message: string): void;
  alertDetails: IAlertDetails | null;
}

export const AlertContext = createContext<IAlertContext | null>(null);

export const useAlertContext = () =>
  useContextWrapper(AlertContext, {
    contextName: useAlertContext.name,
    providerName: AlertContextProvider.name,
  });

const AlertContextProvider = (props: React.PropsWithChildren<{}>) => {
  const [alertDetails, setAlertDetails] =
    useState<IAlertContext['alertDetails']>(null);

  const handleClose = () => setAlertDetails(null);

  const alert = useCallback((type: IAlertDetails['type'], message: string) => {
    setAlertDetails({type, message});
  }, []);

  const value = useMemo(() => ({alert, alertDetails}), [alert, alertDetails]);

  return (
    <AlertContext.Provider value={value}>
      {alertDetails && (
        <Alert
          className="custom-alert"
          dismissible
          variant={alertDetails.type}
          onClose={handleClose}
        >
          <div>{alertDetails.message}</div>
        </Alert>
      )}
      {props.children}
    </AlertContext.Provider>
  );
};

export default AlertContextProvider;
