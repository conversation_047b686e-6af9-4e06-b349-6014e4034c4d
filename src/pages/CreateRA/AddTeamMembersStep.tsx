import React, {forwardRef, useEffect, useImperativeHandle} from 'react';
import {RiskForm} from '../../types/risk';
import {format} from 'date-fns';
import {Card} from 'react-bootstrap';
import {CrewIcon, CrossIcon} from '../../utils/svgIcons';
import SearchCrewMember, {
  AsyncSearchCrewMember,
} from '../../components/SearchCrewMember';
import {UsernameProfile} from '../../components/UsernameProfile';
import {CrewMember, OfficeApprover} from '../../types';
import {useDataStoreContext} from '../../context';
import {getCrewList, getOfficeApprovers} from '../../services/services';
import LevelOfRATag from '../../components/LevelOfRATag';

interface AddTeamMembersStepRefProps {
  form: RiskForm;
  setForm: React.Dispatch<React.SetStateAction<RiskForm>>;
  onValidate: (isValid: boolean) => void;
  isEdit?: boolean;
}

export interface AddTeamMembersStepRef {
  validate: () => boolean;
}

// --- Helper Components ---
const TeamMemberCard: React.FC<{
  member: any;
  onRemove: (id: number | string) => void;
  isEdit?: boolean;
}> = ({member, onRemove, isEdit = false}) => {
  console.log('zzzz member', {member});
  // Check if it's a crew member or office member
  const isCrewMember = !!member.seafarer_id;
  const displayName = isCrewMember
    ? member.seafarer_name
    : `${member.user_name}`.trim();
  const subText = isCrewMember
    ? `${member.seafarer_rank} • HK ID: ${member.seafarer_hkid}`
    : `${member.rank || ''} • ${member.email || ''}`;
  const memberId = isCrewMember ? member.seafarer_id : member.keycloak_id;

  return (
    <div
      className={`d-flex align-items-center justify-content-between border rounded ${
        isEdit ? 'crew-member-profile-edit' : 'crew-member-profile'
      }`}
    >
      <div className="d-flex align-items-center">
        <UsernameProfile username={displayName} subText={subText} />
      </div>
      <button type="button" className="btn" onClick={() => onRemove(memberId)}>
        <CrossIcon />
      </button>
    </div>
  );
};

const TeamMemberList: React.FC<{
  members: any[];
  onRemove: (id: number | string) => void;
  isEdit?: boolean;
}> = ({members, onRemove, isEdit = false}) => (
  <div>
    <div className="d-flex flex-wrap gap-24p">
      {members.map(member => {
        const key =
          'seafarer_id' in member ? member.seafarer_id : member.keycloak_id;
        return (
          <TeamMemberCard
            key={key}
            member={member}
            onRemove={onRemove}
            isEdit={isEdit}
          />
        );
      })}
    </div>
  </div>
);

const EmptyTeamMemberState: React.FC = () => (
  <div className="d-flex flex-column align-items-center gap-16px">
    <CrewIcon />
    <div className="fs-14 text-muted text-center">
      Search and Add the Team Members <br />
      involved in preparing the Risk Assessment
    </div>
  </div>
);

const TaskHeader: React.FC<{form: RiskForm}> = ({form}) => (
  <div className="d-flex justify-content-between align-items-center mb-3">
    <div>
      <div className="secondary-color fs-20 fw-600">
        {form?.task_requiring_ra || ''}
      </div>
      <div className="d-flex">
        {'date_risk_assessment' in form && form.date_risk_assessment && (
          <div className="text-muted fs-14">
            Date of Risk Assessment:{' '}
            {format(new Date(form.date_risk_assessment), 'dd MMM yyyy')}
          </div>
        )}
        {form?.ra_level === 4 && <LevelOfRATag />}
      </div>
    </div>
  </div>
);

// --- Utility Functions ---
const fetchCrewListForVessel = async (
  form: RiskForm,
  setCrewList: React.Dispatch<React.SetStateAction<any[]>>,
  crewMembersListForRisk: CrewMember[],
) => {
  try {
    if ('vessel_id' in form) {
      const vesselId = form.vessel_id;
      if (vesselId) {
        const crewList = crewMembersListForRisk?.length
          ? crewMembersListForRisk
          : await getCrewList(vesselId);
        setCrewList(crewList);
      }
    }
  } catch (error) {
    console.error('Error fetching options:', error);
  }
};

const fetchOfficeApprovers = async (search?: string) => {
  if (!search || search.trim().length < 3) {
    return {options: [], originalData: []};
  }

  const data = await getOfficeApprovers(search);
  return {
    options: data.map(approver => ({
      id: approver.user_id,
      full_name: [approver.first_name, approver.last_name]
        .filter(Boolean)
        .join(' '),
      subText: [approver.rank, approver.email].filter(Boolean).join(' • '),
    })),
    originalData: data,
  };
};

const addTeamMember = (
  selectedIds: string[],
  form: RiskForm,
  crewList: any[],
  setForm: React.Dispatch<React.SetStateAction<RiskForm>>,
  originalData?: any[],
) => {
  if (selectedIds.length === 0 || !('risk_team_member' in form)) return;

  const selectedId = selectedIds[0];

  // Check if we're dealing with crew members or office members
  const hasVesselId = 'vessel_id' in form && form.vessel_id;

  if (hasVesselId) {
    // Handle crew members
    const selectedCrewMember = crewList.find(
      crew => crew.seafarer_id.toString() === selectedId,
    );
    if (!selectedCrewMember) return;

    const exists = form?.risk_team_member?.some(
      member =>
        'seafarer_id' in member &&
        member.seafarer_id === selectedCrewMember.seafarer_id,
    );
    if (exists) return;

    const newTeamMember = {
      seafarer_id: selectedCrewMember.seafarer_id,
      seafarer_person_id: selectedCrewMember.seafarer_person_id,
      seafarer_hkid: selectedCrewMember.seafarer_hkid,
      seafarer_rank_id: selectedCrewMember.seafarer_rank_id,
      seafarer_name: selectedCrewMember.seafarer_name,
      seafarer_rank: selectedCrewMember.seafarer_rank,
      seafarer_rank_sort_order: selectedCrewMember.seafarer_rank_sort_order,
    };

    setForm(prevForm => ({
      ...prevForm,
      risk_team_member: [...prevForm.risk_team_member, newTeamMember],
    }));
  } else {
    // Handle office members
    const selectedOfficeApprover = originalData?.find(
      approver => approver.user_id === selectedId,
    );
    if (!selectedOfficeApprover) return;

    const exists = form?.risk_team_member?.some(
      member =>
        'keycloak_id' in member &&
        member.keycloak_id === selectedOfficeApprover.user_id,
    );
    if (exists) return;

    const newTeamMember = {
      keycloak_id: selectedOfficeApprover.user_id,
      user_name:
        selectedOfficeApprover.first_name +
        (selectedOfficeApprover.last_name
          ? ' ' + selectedOfficeApprover.last_name
          : ''),
      rank: selectedOfficeApprover.rank,
      email: selectedOfficeApprover.email,
    };

    setForm(prevForm => ({
      ...prevForm,
      risk_team_member: [...prevForm.risk_team_member, newTeamMember as any],
    }));
  }
};

const removeTeamMember = (
  id: number | string,
  setForm: React.Dispatch<React.SetStateAction<RiskForm>>,
) => {
  setForm(prevForm => ({
    ...prevForm,
    risk_team_member:
      prevForm?.risk_team_member?.filter(m => {
        // Check if it's a crew member or office member
        if ('seafarer_id' in m) {
          return m.seafarer_id !== id;
        } else if ('keycloak_id' in m) {
          return (m as any).keycloak_id !== id;
        }
        return true;
      }) || [],
  }));
};

const validateTeamMembers = (
  form: RiskForm,
  onValidate: (isValid: boolean) => void,
): boolean => {
  const valid = form?.risk_team_member?.length > 0;
  onValidate(valid);
  return valid;
};

// --- Main Component ---
export const AddTeamMembersStep = forwardRef<
  AddTeamMembersStepRef,
  AddTeamMembersStepRefProps
>(({form, setForm, onValidate, isEdit = false}, ref) => {
  const {
    dataStore: {crewMembersListForRisk},
  } = useDataStoreContext();
  console.log('zzzz', {form});
  const [crewList, setCrewList] = React.useState<CrewMember[]>([]);

  // Check if vessel_id exists in form
  const hasVesselId = 'vessel_id' in form && form.vessel_id;

  useEffect(() => {
    if (hasVesselId) {
      fetchCrewListForVessel(form, setCrewList, crewMembersListForRisk);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [form]);

  const validate = () => validateTeamMembers(form, onValidate);

  useImperativeHandle(ref, () => ({
    validate,
  }));

  useEffect(() => {
    if ('risk_team_member' in form) {
      validate();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [form]);

  const handleTeamMemberSelection = (
    selectedIds: string[],
    originalData?: any[],
  ) => addTeamMember(selectedIds, form, crewList, setForm, originalData);

  const handleRemoveMember = (id: number | string) =>
    removeTeamMember(id, setForm);

  const teamMembers = form?.risk_team_member || [];

  return (
    <>
      {!isEdit && (
        <>
          <TaskHeader form={form} />
          <hr style={{marginRight: '-1rem', marginLeft: '-1rem'}} />
          <div className="secondary-color fs-20 fw-600 mb-4">
            Add Team Members
          </div>{' '}
        </>
      )}
      <div className="mb-4 search-crew-bar">
        {hasVesselId ? (
          <SearchCrewMember
            value={[]}
            options={crewList.map(user => ({
              id: user.seafarer_id.toString(),
              full_name: String(user.seafarer_name),
              subText: `${user.seafarer_rank} • HK ID: ${user.seafarer_hkid}`,
            }))}
            placeholder="Search Name, Rank or HKID to add"
            onChange={handleTeamMemberSelection}
          />
        ) : (
          <AsyncSearchCrewMember<OfficeApprover>
            value={[]}
            placeholder="Search Name, Rank or Email ID to add"
            onChange={handleTeamMemberSelection}
            fetchQuery={fetchOfficeApprovers}
            uniqueQueryKey="getOfficeApprovers-team-members"
          />
        )}
      </div>
      <Card className={`h-60vh ${isEdit ? 'border-0 shadow-none' : ''}`}>
        <Card.Body
          className={
            teamMembers.length === 0
              ? 'd-flex justify-content-center align-items-center'
              : 'p-3'
          }
        >
          {teamMembers.length === 0 ? (
            <EmptyTeamMemberState />
          ) : (
            <TeamMemberList
              members={teamMembers}
              onRemove={handleRemoveMember}
              isEdit={isEdit}
            />
          )}
        </Card.Body>
      </Card>
    </>
  );
});

AddTeamMembersStep.displayName = 'AddTeamMembersStep';
