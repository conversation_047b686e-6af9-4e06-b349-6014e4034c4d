import React from 'react';
import {Button} from 'react-bootstrap';
import {useNavigate} from 'react-router-dom';
import classNames from 'classnames';
import {useDataStoreContext} from '../../../context';
import {PlusIcon} from '../../../components/icons';

interface TemplateListingHeaderProps {
  className?: string;
}

export const TemplateListingHeader: React.FC<TemplateListingHeaderProps> =
  props => {
    const {roleConfig} = useDataStoreContext();
    const navigate = useNavigate();

    return (
      <div
        className={classNames(
          'd-flex justify-content-between align-items-center',
          props.className,
        )}
      >
        <div className="ra-breadcrumb d-flex align-items-center">
          <button
            className="header"
            type="button"
            onClick={() => navigate('/risk-assessment')}
          >
            Risk Assessment
          </button>
          <div className="sub-header">&nbsp;/&nbsp;Templates</div>
        </div>
        {roleConfig.riskAssessment.canCreateNewTemplate && (
          <Button
            variant="primary"
            className="create-new-btn"
            onClick={() => {
              navigate('/risk-assessment/templates/create');
            }}
            data-testid="add-file-btn"
          >
            <span className="d-flex">
              <PlusIcon className="mr-1" />
              Create New
            </span>
          </Button>
        )}
      </div>
    );
  };
