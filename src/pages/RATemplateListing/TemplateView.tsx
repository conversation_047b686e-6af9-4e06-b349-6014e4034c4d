import React, {useEffect, useState} from 'react';
import {useParams} from 'react-router-dom';
import {TemplateForm} from '../../types';
import {createFormFromData} from '../../utils/helper';
import {getTemplateById} from '../../services/services';
import Loader from '../../components/Loader';
import PreviewFormDetails from '../CreateRA/PreviewFormDetails';

export default function TemplateView() {
  const params = useParams<{id: string}>();
  const id = String(params.id);
  const [isLoading, setIsLoading] = useState(true);
  const [form, setForm] = useState<TemplateForm>(createFormFromData());

  const fetchTemplateData = async (templateId: string) => {
    setIsLoading(true);
    try {
      const response = await getTemplateById(templateId);
      const data = response.result;
      const formData = createFormFromData(data);
      setForm(formData);
    } catch (err) {
      console.error('Error fetching draft', err);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (!id) return;
    fetchTemplateData(id);
  }, [id]);

  return (
    <>
      {isLoading && <Loader isOverlayLoader />}
      <PreviewFormDetails
        form={form}
        setForm={setForm}
        atRiskRef={{current: null}}
        handlePreviewPublush={() => {}}
        handleSaveToDraft={() => {}}
        type="template"
        previewOnly
        showBreadCrumb
        showUseTemplate
        breadcrumbOptions={{
          items: [
            {title: 'Risk Assessment', link: '/risk-assessment'},
            {title: 'Templates', link: '/risk-assessment/template-listing'},
            {title: form?.task_requiring_ra || ''}, // No link, just text
          ],
        }}
      />
    </>
  );
}
