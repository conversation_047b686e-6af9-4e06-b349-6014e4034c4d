import React from 'react';
import ReactDOMClient from 'react-dom/client'; // Updated for React 18
import singleSpaReact from 'single-spa-react';
import Root from './root.component';

const lifecycles = singleSpaReact({
  React,
  ReactDOMClient, // No changes here
  rootComponent: Root,
  renderType: 'createRoot', // New option to use React 18's createRoot
  errorBoundary(err, info, props) {
    // Customize the root error boundary for your microfrontend here.
    return <div>Something went wrong!</div>;
  },
});

export const {bootstrap, mount, unmount}: any = lifecycles;
