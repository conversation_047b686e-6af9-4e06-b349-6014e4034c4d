{"extends": ["ts-react-important-stuff", "plugin:prettier/recommended", "eslint:recommended", "plugin:react/recommended", "plugin:@typescript-eslint/recommended"], "parser": "@typescript-eslint/parser", "plugins": ["react", "eslint-plugin-react-hooks", "@typescript-eslint"], "rules": {"react-hooks/rules-of-hooks": "error", "react-hooks/exhaustive-deps": "warn", "@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/no-unused-vars": "off", "@typescript-eslint/ban-types": ["error", {"extendDefaults": true, "types": {"{}": false}}]}, "env": {"browser": true, "es2021": true, "jest": true}, "ignorePatterns": ["dist/"]}